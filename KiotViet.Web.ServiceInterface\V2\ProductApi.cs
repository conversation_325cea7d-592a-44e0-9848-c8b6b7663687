using KiotViet.MongoServices.Interface;
using KiotViet.Resources;
using KiotViet.Web.Api.V2.Attributes;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.DeactiveProductBranchCommand;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.MultiProductSaleBranchCommand;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetProductByCode;
using MediatR;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using KiotVietFnB.Application.Driving.Ports.Dtos;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetListProduct;
using Newtonsoft.Json;
using System.Data;
using DocumentFormat.OpenXml.Wordprocessing;
using KiotViet.Services.Interface;
using KiotVietFnB.Application.Driving.Ports.Auth;
using KiotVietFnB.Application.Driving.Ports.Dtos.Attribute;
using KiotViet.Web.Api.V2.DataContract;
using KiotViet.Persistence;
using KiotViet.Web.Api.Extensions;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductBranchQueries.OnHandByBranch;
using KiotVietFnB.Application.Driving.Ports.Dtos.Product;
using KiotViet.Web.Api.V2.DataContract.Product;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductSaleBranchCommand;
using KiotViet.Persistence.Common;
using KiotVietFnB.Domain.AggregateModels.ProductAggregate.Repository;
using KiotVietFnB.Application.Driving.Ports.Queries.BranchQueries.GetListBracnhByRetailerId;
using KiotViet.MongoDb.Entity;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.CreateProductCommand;
using System.IO;
using KiotViet.AmazonS3;
using KiotViet.Utilities;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductCommand;
using ServiceStack.Web;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.DeleteProductCommand;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdatePriceBookDetailCommand;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateAttributeCommand;
using KiotVietFnB.Application.Driving.Ports.Dtos.ProductAttribute;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductUnitCommand;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetProductDetail;
using KiotVietFnB.Application.Driving.Ports.Dtos.ProductTopping;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetListToppingProduct;
using KiotVietFnB.Application.Driving.Ports.Queries;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetListExtraDishProduct;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetSameTypeProduct;
using KiotVietFnB.Application.Driving.Ports.Dtos.ProductImage;
using KiotVietFnB.Application.Driving.Ports.Dtos.ProductUnit;

using KiotVietFnB.Application.Driven.Ports.Mapper;
using KiotVietFnB.Application.Driving.Ports.Queries.CategoryQueries;
using KiotViet.Exceptions;

using System.Text;
using KiotVietFnB.Domain.AggregateModels.BranchAggreate.Repository;
using KiotVietFnB.Domain.AggregateModels.AttributeAggregate.Repository;
using KiotVietFnB.Domain.AggregateModels.PriceBookDetailAggregate.Repository;
using KiotVietFnB.Domain.AggregateModels.CategoryAggregate.Repository;
using KiotVietFnB.Domain.AggregateModels.ProductBranchAggregate.Repository;
using Attribute = KiotVietFnB.Domain.AggregateModels.AttributeAggregate.Models.Attribute;
using PriceBook = KiotVietFnB.Domain.AggregateModels.PriceBookAggregate.Models.PriceBook;
using PriceBookDetail = KiotVietFnB.Domain.AggregateModels.PriceBookDetailAggregate.Models.PriceBookDetail;
using ProductBranch = KiotVietFnB.Domain.AggregateModels.ProductBranchAggregate.Models.ProductBranch;
using ProductTopping = KiotVietFnB.Domain.AggregateModels.ProductToppingAggregate.Models.ProductTopping;
using StockTake = KiotVietFnB.Domain.AggregateModels.StockTakeAggregate.Models.StockTake;
using KiotVietFnB.Application.Driving.Ports.Dtos.PriceBookDetail;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.SearchProductByKey;
using KiotVietFnB.Application.Driving.Ports.Queries.InventoryTrackingQueries.GetList;
using KiotViet.Services.Common;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetLatestPurchasePrice;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductRankCommand;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetAllMenu;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetMenuDetail;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.GenerateProductSameTypeCommand;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.CreateProductSameTypeCommand;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdatePriceBookListProductCommand;
using KiotVietFnB.Application.Driving.Ports.Queries.BranchQueries.GetBranchById;
using KiotViet.Web.Api.V2.DataContract.ProductUsers;
using KiotVietFnB.Application.Driving.Ports.Commands.ProductUserCommands.AdjustProductUser;
using KiotVietFnB.Application.Driving.Ports.Dtos.ProductUsers;
using KiotVietFnB.Application.Driving.Ports.Queries.ProductQueries.GetProductByIds;
using KiotVietFnB.Domain.AggregateModels.PriceBookAggregate.Specifications;
using System.Threading;
using KiotVietFnB.Domain.AggregateModels.ProductSaleBranchAggregate.Repository;
using KiotVietFnB.Domain.AggregateModels.ProductToppingAggregate.Repository;
using KiotVietFnB.Domain.AggregateModels.PriceBookAggregate.Repository;
using KiotVietFnB.Application.Driving.Ports.Queries.Tax.FetchTaxByModifiedDate;

namespace KiotViet.Web.Api.V2
{
    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
	[RequiredPermission(Product._Read)]
	[Route("/v2/products", "GET", Summary = "API lấy thông tin danh sách hàng hóa",
       Notes = "API lấy thông tin danh sách hàng hóa theo chi nhánh")]
    public class ProductListGet : PageRequest, IHasBranchIds, IReturn<ListProductModel>
    {
        [ApiMember(Name = "ProductKey", Description = "Tên hoặc mã  hàng ",
        ParameterType = "Query", DataType = "String", IsRequired = false)]
        public string ProductKey { get; set; }

        [ApiMember(Name = "ProductTypes", Description = "Loại Hàng: 1: Combo - đóng gói, 2: Hàng hóa thường, 3: Dịch vụ, 4: Hàng sản xuất, 5: Hàng chế biến",
        ParameterType = "Query", DataType = "Array", IsRequired = false)]
        public int[] ProductTypes { get; set; } = null;

        [ApiMember(Name = "ProductGroups", Description = "Loại Thực đơn: 1: Đồ ăn, 2: Đồ uống, 3: Khác, NULL: Tất cả",
        ParameterType = "Query", DataType = "Array", IsRequired = false)]
        public int[] ProductGroups { get; set; } = null;

		[ApiMember(Name = "BranchIds", Description = "Danh sách chi nhánh",
	    ParameterType = "Query", DataType = "Array", IsRequired = false)]
		public int[] BranchIds { get; set; } = null;

		[ApiMember(Name = "TaxId", Description = "Filter hàng hóa có sử dụng thuế",
        ParameterType = "Query", DataType = "Long", IsRequired = false)]
        public long? TaxId { get; set; }

        [ApiMember(Name = "IsRewardPoint", Description = "Tích Điểm: true: hàng tích điểm, false: hàng không tích điểm",
        ParameterType = "Query", DataType = "bool", IsRequired = false)]
        public bool? IsRewardPoint { get; set; }

        [ApiMember(Name = "IsActive", Description = "true: hàng đang kinh doanh, false: hàng ngừng kinh doanh",
        ParameterType = "Query", DataType = "bool", IsRequired = false)]
        public bool? IsActive { get; set; }

        [ApiMember(Name = "AllowsSale", Description = "Filter theo có bán trực tiếp hay không. (AllowsSale=Null: không filler)",
        IsRequired = false)]
        public bool? AllowsSale { get; set; }

        [ApiMember(Name = "CategoryId", Description = "Id của nhóm hàng: nếu CategoryId = 0 sẽ lấy hàng hóa trong mọi nhóm hàng,  nếu CategoryId > 0 sẽ lấy hàng hóa theo Id của nhóm hàng và toàn bộ các nhóm hàng con của nó",
        ParameterType = "Query", DataType = "int", IsRequired = false)]
        public int CategoryId { get; set; }

        [ApiMember(Name = "OnhandFilter", Description = "Lấy danh sách hàng hóa: Dưới định mức tồn OnhandFilter =1, Vượt định mức tồn  OnhandFilter = 2",
        ParameterType = "Query", DataType = "int", IsRequired = false)]
        public byte? OnHandFilter { get; set; }

        [ApiMember(Name = "IncludeRemoved", Description = "Có lấy danh sách Id sản phẩm đã bị xóa dựa theo ModifiedDate hay không (nhằm hỗ trợ đồng bộ dữ liệu dựa theo timeStamp)",
        ParameterType = "Query", DataType = "Date", IsRequired = false)]
        public bool? IncludeRemoved { get; set; }

        [ApiMember(Name = "ModifiedDate", Description = "Ngày sửa", ParameterType = "Query", DataType = "DateTime", IsRequired = false)]
        public DateTime? ModifiedDate { get; set; }

        public long? MasterProductId { get; set; }

        [ApiMember(Name = "AttributeFilter", Description = "Filter theo thuộc tính hàng hóa, sử dụng mảng các object + AttributeId(int): Id của thuộc tính hàng hóa + Attributevalue(string): giá trị thuộc tính hàng hóa theo Id ",
        ParameterType = "Query", DataType = "String", IsRequired = false)]
        public string AttributeFilter { get; set; }

        [ApiMember(Name = "ShelvesIds", Description = "Filter theo vị trí hàng hóa, chứa danh sách id của vị trí, cách nhau bởi dấu ','",
        ParameterType = "Query", DataType = "String", IsRequired = false)]
        public string ShelvesIds { get; set; }
        public IList<long> ProductIds { get; set; }

    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/get-by-code", "GET",
     Summary = @"Lấy danh sách hàng hóa theo mã hàng hóa truyền vào")]
    public class GetProductByCode : IReturn<List<ProductDto>>
    {
        [Description("Danh sách mã hàng hóa ([\"SP000024\",\"SP000005\"] )")]
        public string[] Codes { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Product._Read)]
    [Route("/v2/products/{Id}", "GET", Summary = "API lấy thông tin chi tiết hàng hóa", Notes = "API lấy thông tin chi tiết hàng hóa")]
    public class ProductDetailGet : IReturn<ProductDetailModel>, DataContract.IHasBranchIds
    {
        [ApiMember(Name = "Id", Description = "Id hàng hóa")]
        public long Id { get; set; }
        [ApiMember(Name = "BranchIds", Description = "Danh sách branchIds đang chọn (phục vụ cho check allow sale for branch của hàng hóa)")]
        public int[] BranchIds { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Product._Read)]
    [Route("/v2/products/get-code", "GET", Summary = "API lấy thông tin chi tiết hàng hóa", Notes = "API lấy thông tin chi tiết hàng hóa")]
    public class ProductDetailGetByCode : IReturn<ProductDetailModel>, DataContract.IHasBranchIds
    {
        [ApiMember(Name = "BranchIds", Description = "Danh sách branchIds đang chọn (phục vụ cho check allow sale for branch của hàng hóa)")]
        public int[] BranchIds { get; set; }
        public string Code { get; set; }
    }

    [ExportApiDocument]
	[Tag(nameof(ProductApi))]
	[RequiredPermission(Product._Read)]
	[Route("/v2/products/detail/{Id}", "GET", Summary = "API lấy thông tin chi tiết hàng hóa",
    Notes = "API lấy thông tin chi tiết hàng hóa")]
	public class ProductDetailInfoGet : IReturn<ProductDetailInfoModel>, IHasBranchIds
	{
		[ApiMember(Name = "Id", Description = "Id hàng hóa", ParameterType = "path", DataType = "long", IsRequired = true)]
		public long Id { get; set; }
		[ApiMember(Name = "BranchIds", Description = "Danh sách branchIds đang chọn (phục vụ cho check allow sale for branch của hàng hóa)", ParameterType = "path", DataType = "int[]", IsRequired = true)]
		public int[] BranchIds { get; set; }
	}

	[ExportApiDocument]
	[Tag(nameof(ProductApi))]
	[RequiresAnyPermission(Product._Read)]
	[Route("/v2/products/onhand-by-branch", "GET", Summary = "Tồn kho sản phẩm theo chi nhánh",
	Notes = "Tồn kho sản phẩm theo chi nhánh")]
	public class ProductOnHandByBranchRequest : PageRequest, IHasBranchIds, IReturn<PagingProductOnHandByBranchModel>
	{
		[ApiMember(Name = "ProductId", Description = "ID sản phẩm", IsRequired = true)]
		public long ProductId { get; set; }

		[ApiMember(Name = "BranchIds", Description = "Danh sách Id của chi nhánh. Nếu không truyền tham số này thì sẽ mặc định lấy toàn bộ chi nhánh người dùng có quyền",
		ParameterType = "Query", DataType = "int", IsRequired = false)]
		public int[] BranchIds { get; set; }
	}

	[ExportApiDocument]
	[Tag(nameof(ProductApi))]
	[Route("/v2/products/get-same-type/{productId}", "GET",
	   Summary = @"Lấy danh sách hàng hóa cùng loại")]
	public class GetSameTypeProduct : PageRequest, IReturn<PageResult<ProductModel>>
	{
		public long ProductId { get; set; }
	}

    [ExportApiDocument]
	[RequiresAnyPermission(Product._Read)]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/search", "GET",
      Summary = @"Tìm kiếm hàng hóa theo tên, mã hàng hóa")]
    public class SearchProduct : PageRequest, IReturn<PageResult<ProductSearchModel>>
    {
        [ApiMember(Name = "Ids", Description = "Id product",
        ParameterType = "Query", DataType = "Array", IsRequired = false)]
        public long[] Ids { get; set; }

        [ApiMember(Name = "ProductKey", Description = "Từ khóa tìm kiếm",
        ParameterType = "Query", DataType = "string", IsRequired = false)]
        public string ProductKey { get; set; }

		[ApiMember(Name = "CategoryIds", Description = "Nhóm hàng hóa",
	    ParameterType = "Query", DataType = "Array", IsRequired = false)]
		public int[] CategoryIds { get; set; }

		[ApiMember(Name = "IncludeTimeType", Description = "Bao gồm hàng tính giờ (mặc định = true)",
		ParameterType = "Query", DataType = "Array", IsRequired = false)]
		public bool IncludeTimeType { get; set; } = true;

		[ApiMember(Name = "ProductTypes", Description = "Loại Hàng: 1: Combo - đóng gói, 2: Hàng hóa thường, 3: Dịch vụ, 4: Hàng sản xuất, 5: Hàng chế biến",
	     ParameterType = "Query", DataType = "Int", IsRequired = false)]
		public int[] ProductTypes { get; set; } = null;

		[ApiMember(Name = "IncludeInventoryTrackingIgnore", Description = "Bao gồm hàng đã tắt quản lý tồn kho (mặc định: true - lấy tất cả)",
		ParameterType = "Query", DataType = "bool", IsRequired = false)]
		public bool IncludeInventoryTrackingIgnore { get; set; } = true;

        [ApiMember(Name = "IncludeInActive", Description = "Bao gồm hàng đã ngừng kinh doanh theo chi nhánh hiện tại (mặc định: false - chỉ lấy HH đang bán)",
        ParameterType = "Query", DataType = "bool", IsRequired = false)]
        public bool IncludeInActive { get; set; } = false;
        
		[ApiMember(Name = "OnHandByBranchId", Description = "Trả về thêm giá vốn của branch truyền vào (màn hình chuyển hàng)",
		ParameterType = "Query", DataType = "int", IsRequired = false)]
		public int? OnHandByBranchId { get; set; }


        [ApiMember(Name = "OnlySearchBaseUnit", Description = "Chỉ trả về hàng hóa không có đơn vị và là đơn vị cơ bản (không bao gồm hàng hóa có đơn vị tính là đơn vị quy đồi). Mặc định = false.",
        ParameterType = "Query", DataType = "int", IsRequired = false)]
        public bool? OnlySearchBaseUnit { get; set; }
    }

    [ExportApiDocument]
    [RequiresAnyPermission(Product.PurchasePrice)]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/latest-purchase-price/{ProductId}", "GET", Summary = @"Lấy giá nhập cuối cùng theo chi nhánh hiện tại")]
    public class LatestProductPurchasePriceReq : IReturn<ProductLatestPurchaseModel>
    {
        [ApiMember(Name = "ProductId", Description = "ProductId", ParameterType = "path", DataType = "long", IsRequired = true)]
        public long ProductId { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiresAnyPermission(Product._Read)]
    [Route("/v2/products/generate-same-type", "POST", Summary = @"Generate hàng hóa cùng loại")]
    public class GenerateProductSameType : IReturn<List<ProductSameTypeModel>>
    {
        public long ProductId { get; set; }

        public List<ProductAttributeModel> ProductAttributes { get; set; }
    }    

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiresAnyPermission(Product._Create, Product._Update)]
    [Route("/v2/products/more-same-types", "POST", Summary = @"Tao thêm list hàng hóa cho một hàng cùng loại")]
    public class CreateOrUpdateProductSameType : IReturn<List<ProductSameTypeModel>>
    {
        public long Id { get; set; }

        public bool IsForceUpdate { get; set; }

        public List<ProductSameTypeModel> ProductSameTypes { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiresAnyPermission(Product._Update)]
    [Route("/v2/products/prices", "PUT", Summary = @"Cập giá bán cho hàng hóa theo bảng giá được thiết lập hoặc bảng giá chung")]
    public class UpdatePriceBookListProduct : IReturn<bool>
    {
        public List<ProductPriceBookModel> ProductPriceBooks { get; set; }
        public List<ProductPriceBookModel> ProductPriceBooksRemoves { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiresAnyPermission(Persistence.Product._Read)]
    [Route("/v2/products/generate-new-same-type", "POST", Summary = @"Generate hàng hóa cùng loại (dành riêng cho việc tạo mới từ đầu)")]
    public class GenerateNewProductSameType : IReturn<List<ProductModel>>
    {
        public ProductModel Product { get; set; }
        public List<ProductAttributeModel> ProductAttributes { get; set; }
        public List<ProductUnitModel> Units { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiresAnyPermission(Product._Create)]
    [Route("/v2/products/list-same-types", "POST", Summary = @"Tao mới list hàng hóa cùng loại (dành riêng cho việc tạo mới từ đầu)")]
    public class CreateListProductSameType : IReturn<object>
    {
        public List<ProductModel> Products { get; set; }
        public List<ProductCreateToppingModel> ProductToppings { get; set; }
        public List<ProductUnitModel> Units { get; set; }
        [ApiMember(Name = "RequestStream", Description = "Stream ảnh",
        ParameterType = "Query", DataType = "Stream", IsRequired = false)]
        public Stream RequestStream { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/deactive-product-branch", "POST",
       Summary = @"Không áp dụng kinh doanh hàng hóa theo chi nhánh")]
    public class DeactiveProductSaleBranch : IHasBranchIds, IReturn<bool>
    {
        public long ProductId { get; set; }
        public int[] BranchIds { get; set; }

    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/multi-product-sale-branch", "POST",
       Summary = @"Không áp dụng kinh doanh hàng hóa theo chi nhánh")]
    public class MultiProductSaleBranch : IHasBranchIds, IReturn<bool>
    {
        public List<long> ProductIds { get; set; }

        public int[] BranchIds { get; set; }

        public bool IsGlobal { get; set; }

        public bool ProductTypeActive { get; set; }
    }

  
    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Persistence.Product._Create)]
    [Route("/v2/products/create", "POST",
      Summary = @"Tạo mới hàng hóa")]
    public class CreateProduct : IRequiresRequestStream, IReturn<List<ProductDto>>
    {
        [ApiMember(Name = "Product", Description = "Object hang hoa",
        ParameterType = "Query", DataType = "ProductCreateDto", IsRequired = false)]
        public ProductCreateDto Product { get; set; }

        [ApiMember(Name = "RequestStream", Description = "Stream ảnh",
        ParameterType = "Query", DataType = "Stream", IsRequired = false)]
        public Stream RequestStream { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/update-sale-branch", "POST",
      Summary = @"Update kinh doanh hàng hóa theo chi nhánh")]
    public class UpdateProductSaleBranch : IReturn<bool>
    {
        [ApiMember(Name = "ProductId", Description = "Id hàng hóa",
        ParameterType = "Query", DataType = "String", IsRequired = false)]
        public long ProductId { get; set; }
        [ApiMember(Name = "BranchIds", Description = "Dánh sách chi nhánh kinh doanh",
        ParameterType = "Query", DataType = "String", IsRequired = false)]
        public int[] BranchIds { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/get-list-topping", "POST",
      Summary = @"Lấy danh sách hàng hóa topping")]
    [RequiredPermission(Product._Read)]
    public class GetListToppingProductRequest : PageRequest, IReturn<List<ProductToppingDto>>
    {
        [Description("Tìm kiếm hàng hóa theo Tên hoặc Mã")]
        public string Keyword { get; set; }

        [Description("Danh sách ID nhóm hàng hóa, không truyền sẽ lấy tất cả hàng hóa topping")]
        public List<int> CategoryIds { get; set; }
    }

    [RequiredPermission(Persistence.Product._Update)]
    [Route("/v2/products/update-basic-info", "POST",
      Summary = @"Cập nhật thông tin cơ bản hàng hóa")]
    public class UpdateBasicInfoProduct : IReturn<List<ProductDto>>
    {
        [ApiMember(Name = "Product", Description = "Object hang hoa",
        ParameterType = "Query", DataType = "ProductCreateDto", IsRequired = false)]
        public ProductUpdateDto Product { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Persistence.Product._Update)]
    [Route("/v2/products/update-onhand", "POST",
      Summary = @"Cập nhật tồn kho hàng hóa")]
    public class UpdateOnHandProduct : IReturn<List<ProductDto>>
    {
        [ApiMember(Name = "Product", Description = "Object hang hoa",
        ParameterType = "Query", DataType = "ProductCreateDto", IsRequired = false)]
        public ProductUpdateDto Product { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Product._Update)]
    [Route("/v2/products/update-image", "POST",
      Summary = @"Cập nhật ảnh hàng hóa")]
    public class UpdateImageProduct : IRequiresRequestStream, IReturn<List<ProductDto>>
    {
        [ApiMember(Name = "ProductId", Description = "Id hàng hóa muốn update",
        ParameterType = "Query", DataType = "Stream", IsRequired = false)]
        public long ProductId { get; set; }
        [ApiMember(Name = "RequestStream", Description = "Stream ảnh mới",
        ParameterType = "Query", DataType = "Stream", IsRequired = false)]
        public Stream RequestStream { get; set; }
        [ApiMember(Name = "DeleteImageIds", Description = "Danh sách id ảnh muốn xóa",
        ParameterType = "Query", DataType = "long", IsRequired = false)]
        public List<long> DeleteImageIds { get; set; }
    }

	[ExportApiDocument]
	[Tag(nameof(ProductApi))]
	[RequiredPermission(Persistence.Product._Delete)]
	[Route("/v2/products/{Id}", "DELETE",
	 Summary = @"Cập nhật ảnh hàng hóa")]
	public class DeleteProduct : IReturn<List<ProductDto>>
	{
		[ApiMember(Name = "Id", Description = "Id hàng hóa muốn delete",
		ParameterType = "Query", DataType = "long", IsRequired = false)]
		public long Id { get; set; }
	}

	[ExportApiDocument]
	[Tag(nameof(ProductApi))]
	[RequiresAnyPermission(Persistence.PriceBook._Update, Persistence.PriceBook._Create)]
	[Route("/v2/products/update-price-book-price", "POST",
	Summary = @"Cập nhật ảnh hàng hóa")]
	public class UpdatePriceBookPrice : IReturn<bool>
	{
		[ApiMember(Name = "Product", Description = "Object hang hoa",
	    ParameterType = "Query", DataType = "ProductCreateDto", IsRequired = false)]
		public ProductUpdateDto Product { get; set; }
	}

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Persistence.Product._Update)]
    [Route("/v2/products/update-topping", "POST",
    Summary = @"Cập nhật món thêm hàng hóa")]
    public class UpdateToppingProduct : IReturn<bool>
    {
        [ApiMember(Name = "Product", Description = "Object hàng hóa",
        ParameterType = "Query", DataType = "ProductUpdateDto", IsRequired = false)]
        public ProductUpdateDto Product { get; set; }
    }

	[ExportApiDocument]
	[Tag(nameof(ProductApi))]
	[RequiredPermission(Persistence.Product._Update)]
	[Route("/v2/products/update-unit-name", "POST",
	Summary = @"Cập nhật tên đơn vị tính")]
	public class UpdateUnitNameProduct : IReturn<bool>
	{
		[ApiMember(Name = "ProductId", Description = "Id hàng hóa",
		ParameterType = "Query", DataType = "long", IsRequired = true)]
		public long ProductId { get; set; }
		[ApiMember(Name = "UnitName", Description = "Tên đơn vị",
		ParameterType = "Query", DataType = "string", IsRequired = true)]
		public string UnitName { get; set; }
	}

	[ExportApiDocument]
	[Tag(nameof(ProductApi))]
	[RequiredPermission(Product._Update)]
	[Route("/v2/products/add-unit", "POST",
	Summary = @"Cập nhật / thêm mới đơn vị tính")]
	public class AddProductUnitRequest : IReturn<bool>
	{
		[ApiMember(Name = "ProductId", Description = "Id hàng hóa",
		ParameterType = "Query", DataType = "long", IsRequired = true)]
		public long ProductId { get; set; }

        public List<ProductUnitDto> ProductUnits { get; set; }
	}

	[ExportApiDocument]
	[Tag(nameof(ProductApi))]
	[RequiredPermission(Product._Update)]
	[Route("/v2/products/update-attribute", "POST",
	Summary = @"Cập nhật thuộc tính hóa")]
	public class UpdateAttributeProduct : IReturn<bool>
	{
		[ApiMember(Name = "ProductId", Description = "Id hàng hóa",
		ParameterType = "Query", DataType = "long", IsRequired = true)]
		public long ProductId { get; set; }
		
        [ApiMember(Name = "ProductAttributes", Description = "Tên đơn vị",
		ParameterType = "Query", DataType = "ProductAttributeModel[]", IsRequired = true)]
		public List<ProductAttributeDto> ProductAttributes { get; set; }
	}

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Persistence.Product._Update)]
    [Route("/v2/products/update-formula", "POST",
    Summary = @"Cập nhật hàng hóa thành phần")]
    public class UpdateFormulaProduct : IReturn<ProductUpdateFormulaDto>
    {
        [ApiMember(Description = "Object cập nhật hàng hóa", ParameterType = "body", IsRequired = true)]
        public ProductUpdateFormulaDto Product { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Product.Cost)]
    [Route("/v2/products/update-cost-by-time", "POST",
   Summary = @"Cập nhật thuộc tính hóa")]
    public class UpdateProductCost : IHasBranchIds, IReturn<bool>
    {
        [ApiMember(Name = "BeforeDate", Description = "Thông tin mốc thời gian update",
        ParameterType = "Query", DataType = "long", IsRequired = true)]
        public DateTime BeforeDate { get; set; }

        [ApiMember(Description = "Danh sách Id của chi nhánh. Nếu không truyền tham số này thì sẽ mặc định lấy chi nhánh đang login",
        IsRequired = false)]
        public int[] BranchIds { get; set; }

        [ApiMember(Name = "Products", Description = "Thông tin hàng hóa, giá vốn",
        ParameterType = "Query", DataType = "ProductUpdateCostDto[]", IsRequired = true)]
        public List<ProductUpdateCostDto> Products { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Product.Cost)]
    [Route("/v2/products/update-list-cost", "POST", Summary = @"Cập nhật list giá vốn hàng hóa cho chi nhánh hiện tại")]
    public class UpdateProductCostCurrentBranch : IReturn<bool>
    {
        public List<ProductUpdateCostModel> ProductCosts { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Product._Update)]
    [Route("/v2/products/update-list-onhand", "POST", Summary = @"Cập nhật list tồn kho hàng hóa cho chi nhánh hiện tại")]
    public class UpdateProductOnHandCurrentBranch : IReturn<bool>
    {
        public List<ProductOnHandModel> ProductOnHands { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [RequiredPermission(Product._Update)]
    [Route("/v2/products/update-list-attribute", "POST", Summary = @"Cập nhật danh sách giá trị thuộc tính cho từng hàng hóa", Notes = "Đẩy vào một list, môi hàng hóa chứa giá trị sau khi sửa")]
    public class UpdateListProductAttributeReq : IReturn<bool>
    {
        public List<ProductUpdateListAttributeModel> Products { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/get-list-extra-dish", "POST",
      Summary = @"Lấy danh sách hàng hóa sử dụng làm món thêm")]
    [RequiredPermission(Product._Read)]
    public class GetListExtraDishProductRequest : PageRequest, IReturn<List<ProductDto>>
    {
        [Description("Tìm kiếm hàng hóa theo Tên hoặc Mã")]
        public string Keyword { get; set; }

        [Description("Danh sách ID nhóm hàng hóa, không truyền sẽ lấy tất cả hàng hóa")]
        public List<int> CategoryIds { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/ranks", "POST",
      Summary = @"Cập nhật thứ tự sắp xếp cho hàng hóa")]
    [RequiredPermission(Product._Update, ApplyTo = ApplyTo.Update)]
    public class UpdateRankProductsReq : IReturn<bool>
    {
        public List<ProductRankDto> ProductRanks { get; set; }
        public int BranchId { get; set; }
    }

    [Route("/v2/product-user", "POST", Summary = "API Thêm mới, cập nhật, xóa món có sẵn trong menu theo user",
    Notes = "API Thêm mới, cập nhật, xóa món có sẵn trong menu theo user")]
    public class AdjustProductUserReq : IReturn<bool>
    {
        public List<AdjustProductUserModel> ProductUsers { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/menus", "GET",
      Summary = @"Lấy danh sách thực đơn ")]
    [RequiredPermission(Product._Read)]
    public class GetMenusReq : IReturn<PageResult<MenuDto>>
    {
        [ApiMember(Name = "BranchId", Description = "Chi nhánh", ParameterType = "Query")]
        public int? BranchId { get; set; }
    }

    [ExportApiDocument]
    [Tag(nameof(ProductApi))]
    [Route("/v2/products/menu-details", "GET",
      Summary = @"Chi tiết thực đơn")]
    [RequiredPermission(Product._Read)]
    public class GetMenuDetailsReq : PageRequest, IReturn<MenuDetailDto>
    {
        [ApiMember(Name = "BranchId", Description = "Chi nhánh", ParameterType = "Query")]
        public int? BranchId { get; set; }
        [ApiMember(Name = "CategoryId", Description = "Nhóm sản phẩm", ParameterType = "Query")]
        public int CategoryId { get; set; }
        public bool? IsActive { get; set; }
    }

    public class ProductApi : BaseApi
    {
        public IAuditTrailService AuditTrailLogService { get; set; }
        public IMediator Mediator { get; set; }
        public IMapper Mapper { get; set; }
        public ICategoryService CategoryService { get; set; }
        public IProductRepository ProductRepository { get; set; }
        public IPriceBookRepository PriceBookRepository { get; set; }
        public ICategoryRepository CategoryRepository { get; set; }
        public IProductBranchRepository ProductBranchRepository { get; set; }
        public IProductToppingRepository ProductToppingRepository { get; set; }
        public IPriceBookDetailRepository PriceBookDetailRepository { get; set; }
        public IAttributeRepository AttributeRepository { get; set; }
        public IBranchRepository BranchRepository { get; set; }
        public IProductSaleBranchRepository ProductSaleBranchRepository { get; set; }
        public IKvFnBAuthService KvFnBAuthService { get; set; }
        public IAuthService AuthService { get; set; }

        public IProductService ProductService { get; set; }

        public PosSetting PosSettings { get; set; }

        public async Task<object> Get(GetMenusReq req)
        {
            if ((req.BranchId ?? 0) < 1)
            {
                return Failure(new List<string>() { "Vui lòng chọn chi nhánh" }, nameof(GetMenusReq));
            }

            var branchAuthenIds = AuthService.GetAuthorizedBranches(Product._Read);

            if (!(branchAuthenIds?.Any(x => x == req.BranchId) ?? false))
            {
                return Failure(new List<string>() { "Không có quyền truy cập hoặc chi nhánh không tồn tại" }, nameof(GetMenusReq));
            }                

            var result = await Mediator.Send(new GetAllMenuQuery(req.BranchId));

            if (result.IsSuccess)
            {
                return new PageResult<MenuDto>
                {
                    Data = result.Data,
                    Total = result.Data?.Count ?? 0
                };
            }

            return Failure(result.Errors, nameof(GetMenusReq));
        }
        
        public async Task<object> Get(GetMenuDetailsReq req)
        {
            if ((req.BranchId ?? 0) < 1)
            {
                return Failure(new List<string>() { "Vui lòng chọn chi nhánh" }, nameof(GetMenuDetailsReq));
            }

            var branchAuthenIds = AuthService.GetAuthorizedBranches(Product._Read);

            if (!(branchAuthenIds?.Any(x => x == req.BranchId) ?? false))
            {
                return Failure(new List<string>() { "Không có quyền truy cập hoặc chi nhánh không tồn tại" }, nameof(GetMenusReq));
            }

            var result = await Mediator.Send(new GetMenuDetailQuery(req.BranchId, req.CategoryId, req.Skip, req.Take, req.IsActive));

            if (result.IsSuccess)
            {
                return result.Data;
            }

            return Failure(result.Errors, nameof(GetMenuDetailsReq));
        }

        public async Task<object> Get(ProductListGet req)
        {
			req.ValidateBranchIdsByPermission(Product._Read);
			List<AttributeDto> listAttributeFilter = GetListAttributeFilter(req);
            IQueryable<Persistence.Category> catls = null;
            if (req.CategoryId > 0)
            {
                var cateResult = await Mediator.Send(new GetAllCategoryQuery());
                if (cateResult.IsSuccess)
                {
                    catls = await CategoryService.GetAllSub(req.CategoryId);
                }
                else
                {
                    return Failure(cateResult.Errors, nameof(GetAllCategoryQuery));
                }
            }
            // build filter for queries
            var request = new GetListProductQuery()
            {
                Skip = req.Skip,
                Take = req.Take,
                OrderBy = req.OrderBy,
                SortDirection = req.SortDirection,
                BranchId = KvFnBAuthService.AuthContext.BranchId,
                MasterProductId = req.MasterProductId ?? 0,
                ProductTypes = req.ProductTypes != null && req.ProductTypes.Any() ? req.ProductTypes : null,
                ProductGroups = req.ProductGroups,
                IsActive = req.IsActive,
                IsRewardPoint = req.IsRewardPoint,
                OnhandFilter = req.OnHandFilter ?? 0,
                CategoryIds = catls != null && catls.Any() ? catls.Select(s => s.Id).Join(",") : string.Empty,
                Keyword = req.ProductKey,
                ListAttributeFilter = listAttributeFilter,
                ShelvesIds = req.ShelvesIds,
                FilterTaxId = req.TaxId,
                AllowsSale = req.AllowsSale,
                BranchIds = req.BranchIds,
                IsViewCost = AuthService.CheckPermission(Product.Cost)
            };


            var listProductResult = await Mediator.Send(request);
            if (listProductResult.IsSuccess)
            {
                return Mapper.Map<ListProductDto, ListProductModel>(listProductResult.Data);
            }
            return Failure(listProductResult.Errors, nameof(GetListProductQuery));
        }

		public async Task<object> Get(ProductDetailGet req)
		{
			req.ValidateBranchIdsByPermission(Product._Read);

			var listBranchIdsHasUpdatePermission = AuthService.GetAuthorizedBranches(Product._Update).Distinct().ToArray();

			var result = await Mediator.Send(new GetProductDetailQuery(
				 req.Id,
				 req.BranchIds,
				listBranchIdsHasUpdatePermission
			));
			if (result.IsSuccess)
			{
				return Mapper.Map<ProductDetailDto, ProductDetailModel>(result.Data);
			}

			return Failure(result.Errors, nameof(GetProductDetailQuery));
		}
        
        public async Task<object> Get(ProductDetailGetByCode req)
		{
			req.ValidateBranchIdsByPermission(Product._Read);

			var listBranchIdsHasUpdatePermission = AuthService.GetAuthorizedBranches(Product._Update).Distinct().ToArray();

            if(string.IsNullOrEmpty(req.Code))
            {
                return Failure(new List<string>() { "Vui lòng nhập mã hàng hóa" }, nameof(ProductDetailGetByCode));
            }

            var productBasic = await Mediator.Send(new GetProductByCodeQuery(new string[] { req.Code }));
            if (!productBasic.IsSuccess)
            {
                return Failure(productBasic.Errors, nameof(GetProductByCodeQuery));
            }
            var id = productBasic.Data.Select(x => x.Id).FirstOrDefault() ?? 0;

            var result = await Mediator.Send(new GetProductDetailQuery(
                 id,
				 req.BranchIds,
				listBranchIdsHasUpdatePermission
			));

			if (result.IsSuccess)
			{
				return Mapper.Map<ProductDetailDto, ProductDetailModel>(result.Data);
			}

			return Failure(result.Errors, nameof(GetProductDetailQuery));
		}

		public async Task<object> Get(ProductDetailInfoGet req)
		{
			req.ValidateBranchIdsByPermission(Product._Read);

			var listBranchIdsHasUpdatePermission = AuthService.GetAuthorizedBranches(Product._Update).Distinct().ToArray();

			var result = await Mediator.Send(new GetProductDetailQuery(
				 req.Id,
				 req.BranchIds,
				listBranchIdsHasUpdatePermission
			));
			if (result.IsSuccess)
			{
				return Mapper.Map<ProductDetailDto, ProductDetailInfoModel>(result.Data);
			}

			return Failure(result.Errors, nameof(GetProductDetailQuery));
		}

		public async Task<object> Get(GetProductByCode req)
        {
            var result = await Mediator.Send(new GetProductByCodeQuery(
                                             req.Codes
                                        ));

            if (result.IsSuccess)
            {
                return result.Data;
            }

            return Failure(result.Errors, nameof(GetProductByCodeQuery));
        }

		public async Task<object> Get(GetSameTypeProduct req)
		{
            var branchAuthenIds = AuthService.GetAuthorizedBranches(Product._Read);
            var isViewCost = AuthService.CheckPermission(Product.Cost); 

            var result = await Mediator.Send(new GetSameTypeProductQuery(
											 req.ProductId
                                        )
            {
                OrderBy = req.OrderBy,
                SortDirection = req.SortDirection,
                Take = req.Take,
                Skip = req.Skip,
                BranchPermissionIds = branchAuthenIds?.ToList() ?? new List<int>(),
                IsViewCost = isViewCost
            });

			if (result.IsSuccess)
			{
				return Mapper.Map<PagedResult<ProductDto>, PageResult<ProductModel>>(result.Data);
			}

			return Failure(result.Errors, nameof(GetProductByCodeQuery));
		}

		public async Task<object> Get(ProductOnHandByBranchRequest req)
		{
			req.ValidateBranchIdsByPermission(Product._Read);
			var results = await Mediator.Send(new ProductOnHandByBranchQuery()
			{
				Skip = req.Skip,
				Take = req.Take,
				OrderBy = req.OrderBy,
				SortDirection = req.SortDirection,
				BranchIds = req.BranchIds,
				ProductId = req.ProductId
			});

			if (results.IsSuccess)
			{
                return Mapper.Map<PagingProductOnHandByBranchDto, PagingProductOnHandByBranchModel>(results.Data);
			}

			return Failure(results.Errors, nameof(ProductOnHandByBranchRequest));
		}

        public async Task<object> Get(SearchProduct req)
        {
            if (req.OnHandByBranchId.HasValue)
            {
				var varBranchOnHands = AuthService.GetAuthorizedBranches(Product.OnHand).Distinct().ToArray();
                req.OnHandByBranchId = varBranchOnHands.Contains(req.OnHandByBranchId.Value) ? req.OnHandByBranchId : null;
			}

            var result = await Mediator.Send(
                new SearchProductByKeyQuery(
                    req.Ids,
                    req.ProductKey, 
                    req.CategoryIds,
                    req.ProductTypes,
                    req.IncludeTimeType, 
                    req.IncludeInventoryTrackingIgnore,
                    req.IncludeInActive,
                    req.OnHandByBranchId,
                    req.OnlySearchBaseUnit)
                { 
                    OrderBy = req.OrderBy,
                    Skip = req.Skip,
                    Take = req.Take,
                    SortDirection = req.SortDirection,
                });

            if (result.IsSuccess)
            {
                return new PageResult<ProductSearchModel>()
                {
                     Data = Mapper.Map<List<ProductDto>, List<ProductSearchModel>>(result.Data.Items),
                     Total = result.Data.Total
                };
            }

            return Failure(result.Errors, nameof(GetProductByCodeQuery));
        }

        public async Task<object> Get(LatestProductPurchasePriceReq req)
        {
            var productPurchase = await Mediator.Send(new GetLatestPurchasePriceQuery(req.ProductId));

            if (productPurchase.IsSuccess)
            {
                return Mapper.Map<ProductLatestPurchaseDto, ProductLatestPurchaseModel>(productPurchase.Data);
            }

            return Failure(productPurchase.Errors, nameof(LatestProductPurchasePriceReq));
        }

        public async Task<object> Post(GenerateProductSameType req)
        {
            var productAttributeDtos = Mapper.Map<List<ProductAttributeModel>, List<ProductAttributeDto>>(req.ProductAttributes);

            var result = await Mediator.Send(new GenerateProductSameTypeCommand(req.ProductId, productAttributeDtos));

            if (result.IsSuccess)
            {
                return Mapper.Map<List<ProductSameTypeDto>, List<ProductSameTypeModel>>(result.Data);
            }

            return Failure(result.Errors, nameof(GetListToppingProductRequest));
        }

        public async Task<object> Post(CreateOrUpdateProductSameType req)
        {
            var permissionBranchIds = AuthService.GetAuthorizedBranchesByList(new string[] { Product._Update, Product._Create }).Distinct().ToArray();
            var productSameTypeDtos = Mapper.Map<List<ProductSameTypeModel>, List<ProductSameTypeDto>>(req.ProductSameTypes);
            
            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);

            var result = isUsingProductV2 
                ? await Mediator.Send(
                    new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.CreateOrUpdateProductSameTypeV2Command.CreateOrUpdateProductSameTypeCommand(
                        productSameTypeDtos, permissionBranchIds?.ToList() ?? new List<int>(), req.Id, req.IsForceUpdate))
                : await Mediator.Send(new CreateOrUpdateProductSameTypeCommand(productSameTypeDtos, permissionBranchIds?.ToList() ?? new List<int>(), req.Id, req.IsForceUpdate));

            if (!result.IsSuccess)
            {
                return Failure(result.Errors, nameof(CreateOrUpdateProductSameType));
            }

            await WriteLogProductSameType(result.Data.ProductInserts);

            await WriteLogUpdateAttribute(result.Data.ProductOriginals, result.Data.ProductUpdates);

            return result.Data;
        }

        public async Task<object> Post(GenerateNewProductSameType req)
        {
            var productAttributeDtos = Mapper.Map<List<ProductAttributeModel>, List<ProductAttributeDto>>(req.ProductAttributes);
            var units = Mapper.Map<List<ProductUnitModel>, List<ProductUnitDto>>(req.Units);
            var productDto = Mapper.Map<ProductModel, ProductDto>(req.Product);
            var result = await Mediator.Send(new GenerateNewProductSameTypeCommand(productDto, productAttributeDtos, units));

            if (result.IsSuccess)
            {
                return Mapper.Map<List<ProductDto>, List<ProductModel>>(result.Data);
            }

            return Failure(result.Errors, nameof(GenerateNewProductSameType));
        }
        
        public async Task<object> Post(CreateListProductSameType req)
        {
            var formData = Request.FormData;
            if (formData != null)
            {
                req.Products = formData["Products"] != null ? JsonConvert.DeserializeObject<List<ProductModel>>(formData["Products"]) : req.Products;
                req.ProductToppings = formData["ProductToppings"] != null ? JsonConvert.DeserializeObject<List<ProductCreateToppingModel>>(formData["ProductToppings"]) : req.ProductToppings;
                req.Units = formData["Units"] != null ? JsonConvert.DeserializeObject<List<ProductUnitModel>>(formData["Units"]) : req.Units;
            }

            var permissionBranchIds = AuthService.GetAuthorizedBranchesByList(new string[] { Product._Update, Product._Create }).Distinct().ToArray();
            var productSameTypeDtos = Mapper.Map<List<ProductModel>, List<ProductDto>>(req.Products);
            var productUnitDtos = Mapper.Map<List<ProductUnitModel>, List<ProductUnitDto>>(req.Units);
            
            var productImages = await ProcessProductImages();

            MapImageForProducts(productSameTypeDtos, productImages);
            
            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);

            var result =
                isUsingProductV2 
                ? await Mediator.Send(
                    new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.CreateProductV2Command.CreateListProductCommand(
                        productSameTypeDtos,
                        permissionBranchIds?.ToList() ?? new List<int>(),
                        req.ProductToppings?.Select(x => x.ProductId).ToList() ?? new List<long>(),
                        productUnitDtos
                        )
                    )
                :
                await Mediator.Send(
                    new CreateListProductCommand(
                        productSameTypeDtos,
                        permissionBranchIds?.ToList() ?? new List<int>(),
                        req.ProductToppings?.Select(x => x.ProductId).ToList() ?? new List<long>(),
                        productUnitDtos
                        )
                    );

            if (!result.IsSuccess)
            {
                return Failure(result.Errors, nameof(CreateListProductSameType));
            }

            await WriteLogProductSameType(result.Data, req.ProductToppings);

            var product = result.Data.FirstOrDefault(x => x.MasterProductId == null || x.MasterProductId == 0);

            return new
            {
                product.Id,
                product.Code,
                product.Name,
                product.MasterProductId,
            };
        }

        public async Task<object> Post(DeactiveProductSaleBranch req)
        {
            var result = await Mediator.Send(new DeactiveProductBranchCommand(
                 req.ProductId,
                 req.BranchIds?.ToList() ?? new List<int>(),
                 AuthService.GetAuthorizedBranches(Product._Update).Distinct().ToList()
            ));

            if (result.IsSuccess)
            {
                return true;
            }

            return Failure(result.Errors, nameof(DeactiveProductBranchCommand));
        }

        public async Task<object> Post(CreateProduct req)
        {
            var formData = Request.FormData;
            if (formData != null)
            {
                req.Product = JsonConvert.DeserializeObject<ProductCreateDto>(formData["Product"]);
            }
            req.Product.ProductImages = await ProcessProductImages();
            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);
            var result = isUsingProductV2
                ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.CreateProductV2Command.CreateProductCommand(
                    req.Product, AuthService.GetAuthorizedBranches(Product._Create).Distinct().ToList()))
                : await Mediator.Send(new CreateProductCommand(req.Product, AuthService.GetAuthorizedBranches(Product._Create).Distinct().ToList()));

            if (result.IsSuccess)
            {
                await WriteLogCreateProduct(result.Data, req.Product);
                return new PagedResult<ProductDto>
                {
                    Items = result.Data,
                    Total = result.Data.Count
                };
            }

            return Failure(result.Errors);
        }

        public async Task<object> Post(UpdateBasicInfoProduct req)
        {
            KiotVietFnB.Domain.AggregateModels.ProductAggregate.Models.Product oldProduct = null;
            decimal oldCost = 0;
            try
            {
                oldProduct = await ProductRepository.GetAsync(req.Product.Id);
                oldCost = (await ProductBranchRepository.GetOne(req.Product.Id, CurrentBranchId))?.Cost ?? 0;
            }
            catch (Exception e) { Log.Error(e.Message, e); }

            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);

            var result = isUsingProductV2 
                ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductV2Command.UpdateBasicInfoProductCommand(req.Product)) 
                : await Mediator.Send(new UpdateBasicInfoProductCommand(req.Product));

            if (result.IsSuccess)
            {
                await WriteLogUpdateBasicInfoProduct(oldProduct, result.Data, oldCost);
                return true;
            }


            return Failure(result.Errors);
        }

        public async Task<object> Post(UpdateOnHandProduct req)
        {
            var oldOnHand = await ProductBranchRepository.GetOne(productId: req.Product.Id, CurrentBranchId);
            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);

            var result = isUsingProductV2 
                ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductV2Command.UpdateOnHandProductCommand(req.Product)) 
                : await Mediator.Send(new UpdateOnHandProductCommand(req.Product));

            if (result.IsSuccess)
            {
                await WriteLogUpdateOnHand(req.Product.Id, oldOnHand, req.Product, result.Data);
                return true;
            }


            return Failure(result.Errors);
        }

        public async Task<object> Post(UpdateImageProduct req)
        {
            var formData = Request.FormData;
            if (formData != null)
            {
                req.ProductId = formData["ProductId"] != null ? JsonConvert.DeserializeObject<long>(formData["ProductId"]) : 0;
                req.DeleteImageIds = formData["DeleteImageIds"] != null ? JsonConvert.DeserializeObject<List<long>>(formData["DeleteImageIds"]) : new List<long>();
            }
            var newImages = await ProcessProductImages();

            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);

            var result =
                isUsingProductV2 
                ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductV2Command.UpdateImageProductCommand(
                    req.ProductId,
                    newImages.Select(i => i.Image).ToList(),
                    req.DeleteImageIds
                )) :
                await Mediator.Send(new UpdateImageProductCommand(
                 req.ProductId,
                 newImages.Select(i => i.Image).ToList(),
                 req.DeleteImageIds
            ));

            if (result.IsSuccess)
            {
                await WriteLogUpdateImage(result.Data);
                return true;
            }

            return Failure(result.Errors);
        }

		public async Task<object> Post(UpdatePriceBookPrice req)
		{
            var listOldPriceBookDetails = await PriceBookDetailRepository.GetByProductIdAsync(req.Product.Id);
            
            var result = await Mediator.Send(new UpdatePriceBookDetailCommand(
                req.Product.PriceBooks,
				req.Product.Id
			));

			if (result.IsSuccess)
			{
                await WriteLogUpdatePriceBook(req.Product.Id, req.Product.PriceBooks, listOldPriceBookDetails);
                return true;
			}

			return Failure(result.Errors);
		}

        public async Task<object> Post(UpdateToppingProduct req)
        {
            List<ProductTopping> oldToppings = null;
            try
            {
                var oldProduct = await ProductRepository.GetAsync(req.Product.Id);
                oldToppings = oldProduct.IsTopping == true
                    ? await ProductToppingRepository.GetByToppingId(oldProduct.Id)
                    : await ProductToppingRepository.GetByProductId(oldProduct.Id);
            }
            catch (Exception e) { Log.Error(e.Message, e); }

            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);

            var result = isUsingProductV2
                ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductV2Command.UpdateToppingProductCommand(req.Product)) 
                : await Mediator.Send(new UpdateToppingProductCommand(req.Product));

            if (result.IsSuccess)
            {
                await WriteLogUpdateTopping(result.Data, req.Product, oldToppings);
                return true;
            }

            return Failure(result.Errors);
        }

        public async Task<object> Post(MultiProductSaleBranch req)
        {
            var result = await Mediator.Send(new MultiProductSaleBranchCommand(
                                             req.ProductIds,
                                              req.BranchIds?.ToList() ?? new List<int>(),
                                             req.IsGlobal,
                                             req.ProductTypeActive,
                                             AuthService.GetAuthorizedBranches(Product._Update).Distinct().ToList()
                                        ));

            if (result.IsSuccess)
            {
                return true;
            }

            return Failure(result.Errors, nameof(MultiProductSaleBranchCommand));
        }

		public async Task<object> Post(GetListToppingProductRequest req)
		{
			var result = await Mediator.Send(new GetListToppingProductQuery
			{
				Keyword = req.Keyword,
				CategoryIds = req.CategoryIds,
				Skip = req.Skip,
				Take = req.Take,
				OrderBy = req.OrderBy,
				SortDirection = req.SortDirection
			});

			if (result.IsSuccess)
			{
				return Mapper.Map<PagedResult<ProductToppingDto>, PageResult<ProductToppingModel>>(result.Data);
			}

			return Failure(result.Errors, nameof(GetListToppingProductQuery));
		}

		public async Task<object> Post(GetListExtraDishProductRequest req)
		{
			var result = await Mediator.Send(new GetListExtraDishProductQuery
			{
				Keyword = req.Keyword,
				CategoryIds = req.CategoryIds,
				OrderBy = req.OrderBy,
				SortDirection = req.SortDirection,
				Skip = req.Skip,
				Take = req.Take
			});

			if (result.IsSuccess)
			{
				return result.Data;
			}

			return Failure(result.Errors, nameof(GetListExtraDishProductQuery));
        }

        public async Task<object> Post(UpdateProductSaleBranch req)
        {
			var result = await Mediator.Send(new UpdateProductSaleBranchCommand(
                                             req.ProductId,
                                             req.BranchIds?.ToList() ?? new List<int>(),
                                             AuthService.GetAuthorizedBranches(Product._Update).Distinct().ToList()
                                        ));


            if (result.IsSuccess)
            {
                #region Log
                try
                {
                    var branchResults = await Mediator.Send(new GetListBranchByRetailerIdQuery()
                    {
                        Take = 50,
                        BranchIds = req.BranchIds
                    });
                    if (branchResults.IsSuccess)
                    {
                        var branchText = string.Join(",", branchResults.Data.Items.Select(b => b.Name));
                        var product = await ProductRepository.GetAsync(req.ProductId);
                        var msg = $"Thay đổi chi nhánh kinh doanh hàng hóa: {product.Name} </br> {req.BranchIds.Count()} chi nhánh: {branchText}";

                        var log = new AuditTrailLog
                        {
                            FunctionId = (int)FunctionType.Product,
                            Action = (int)AuditTrailAction.Update,
                            Content = msg
                        };
                        _ = AuditTrailLogService.AddLog(log);
                    }

                }
                #endregion Log
                catch (Exception ex)
                {
                    Log.Error(ex);
                }
                return true;
            }

            return Failure(result.Errors);
        }

		public async Task<object> Post(UpdateUnitNameProduct req)
		{
            var oldProduct = await ProductRepository.GetAsync(req.ProductId);
            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);

            var result = isUsingProductV2
                ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductV2Command.UpdateProductUnitNameCommand(
                    req.ProductId,
                    req.UnitName
                )) : await Mediator.Send(new UpdateProductUnitNameCommand(req.ProductId, req.UnitName));

			if (result.IsSuccess)
			{
                await WriteLogUpdateUnitName(result.Data, oldProduct.Unit);
                return true;
			}

			return Failure(result.Errors);
		}

		public async Task<object> Post(AddProductUnitRequest req)
		{
            var oldProduct = await ProductRepository.GetAsync(req.ProductId);
            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);
            
            var result = isUsingProductV2
                ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductUnitV2Command.UpdateProductUnitCommand(
                    req.ProductId,
                    req.ProductUnits
                ))
                : await Mediator.Send(new UpdateProductUnitCommand(
                    req.ProductId,
                    req.ProductUnits
                ));

			if (result.IsSuccess)
			{
                await WriteLogAddProductUnit(oldProduct, result.Data);
                return true;
			}

			return Failure(result.Errors);
		}

		public async Task<object> Post(UpdateAttributeProduct req)
		{
            var product = await ProductRepository.GetAsync(req.ProductId);
            var productDto = Mapper.Map<KiotVietFnB.Domain.AggregateModels.ProductAggregate.Models.Product, ProductDto>(product);
            var listBeforProduct = new List<ProductDto>() { productDto };
            if (product != null)
            {
                var productSrc = await ProductRepository.GetProductSameUnit(product.Id, product.Unit);
                if (productSrc != null)
                {
                    var produdctDtos = Mapper.Map<List<KiotVietFnB.Domain.AggregateModels.ProductAggregate.Models.Product>, List<ProductDto>>(productSrc);
                    listBeforProduct.AddRange(produdctDtos);
                }
            }

            var result = await Mediator.Send(new UpdateProductAttributeCommand(
				 req.ProductId,
				 req.ProductAttributes
			));

			if (result.IsSuccess)
			{
                await WriteLogUpdateAttribute(listBeforProduct, result.Data);
                return true;
			}

			return Failure(result.Errors);
		}

        public async Task<object> Post(UpdateProductCost req)
        {
            req.BranchIds = req.BranchIds != null && req.BranchIds.Any() ? req.BranchIds : new int[] { CurrentBranchId };
            req.ValidateBranchIdsByPermission(Product.Cost);

            if (req.BeforeDate == default)
            {
                throw new KvValidateException("Chưa nhập thời gian");
            }
            if (req.Products == null || !req.Products.Any())
            {
                throw new KvValidateException("Chưa có thông tin hàng hóa");
            }
            if (req.Products.Count > 100)
            {
                throw new KvValidateException("Quá số lương giới hạn 100 hàng hóa");
            }

            var productIds = req.Products.Select(p => p.Id).Distinct();
            var products = await ProductRepository.GetByIdsAsync(productIds.ToList());
            var branchs = await BranchRepository.GetByIdsAsync(req.BranchIds.ToList());
            await UpdateProductCost(req, products, branchs);
            return true;
        }

        public async Task<object> Post(UpdateProductCostCurrentBranch req)
        {
            if (req.ProductCosts == null || !req.ProductCosts.Any())
            {
                throw new KvValidateException("Chưa có thông tin hàng hóa");
            }
            if (req.ProductCosts.Count > 100)
            {
                throw new KvValidateException("Quá số lượng giới hạn 100 hàng hóa");
            }

            if (PosSettings.UseAvgCost)
            {
                throw new KvValidateException("Vui lòng tắt thiết lập giá vốn trung bình để điều chỉnh giá vốn");
            }

            var productCostDtos = Mapper.Map<List<ProductUpdateCostModel>, List<ProductUpdateCostDto>>(req.ProductCosts);

            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);
            var result = isUsingProductV2
                ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductV2Command.UpdateCostListProductCommand(productCostDtos))
                :await Mediator.Send(new UpdateCostListProductCommand(productCostDtos));

            if (!result.IsSuccess)
            {
                return Failure(result.Errors);
            }

            await WriteLogUpdateListCostProductForCurrentBranch(result.Data);

            return true;
        }

        public async Task<object> Post(UpdateProductOnHandCurrentBranch req)
        {
            if (req.ProductOnHands == null || !req.ProductOnHands.Any())
            {
                throw new KvValidateException("Chưa có thông tin hàng hóa");
            }
            if (req.ProductOnHands.Count > 100)
            {
                throw new KvValidateException("Quá số lượng giới hạn 100 hàng hóa");
            }

            var productOnHandDtos = Mapper.Map<List<ProductOnHandModel>, List<ProductOnHandDto>>(req.ProductOnHands);
            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);
            var result = isUsingProductV2
                ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdateProductV2Command.UpdateOnHandListProductCommand(productOnHandDtos))
                : await Mediator.Send(new UpdateOnHandListProductCommand(productOnHandDtos));
            
            if (!result.IsSuccess)
            {
                return Failure(result.Errors);
            }
            await WriteLogUpdateListOnHand(result.Data);
            return true;
        }

        public async Task<object> Post(UpdateListProductAttributeReq req)
        {
            if (req.Products == null || !req.Products.Any())
            {
                throw new KvValidateException("Chưa có thông tin hàng hóa");
            }
            if (req.Products.Count > 100)
            {
                throw new KvValidateException("Quá số lượng giới hạn 100 hàng hóa");
            }

            var productAttributeDtos = Mapper.Map<List<ProductUpdateListAttributeModel>, List<ProductUpdateListAttributeDto>>(req.Products);
            var result = await Mediator.Send(new UpdateListProductAttributeCommand(productAttributeDtos));
            if (!result.IsSuccess)
            {
                return Failure(result.Errors);
            }

            await WriteLogUpdateAttribute(result.Data.OldProducts, result.Data.Products);

            return true;
        }

        public async Task<object> Post(UpdateFormulaProduct req)
        {
            var result = await Mediator.Send(new UpdateProductFormulaCommand(
                 req.Product.ProductId,
                 req.Product.ProductFormulas
            ));

            if (result.IsSuccess)
            {
                await WriteLogUpdateProductFormula(result.Data, req.Product);
                return true;
            }

            return Failure(result.Errors);
        }

        public async Task<object> Post(UpdateRankProductsReq req)
        {
            if (req.BranchId < 1)
            {
                return Failure(new List<string>() { "Vui lòng chọn chi nhánh" }, nameof(UpdateRankProductsReq));
            }

            var branchPermisses = AuthService.GetAuthorizedBranches(Product._Update).Distinct().ToList();
            if (!AuthService.Context.User.IsAdmin && !(branchPermisses?.Any(x => x == req.BranchId) ?? false))
            {
                return Failure(new List<string>() { "Không có quyền thao tác trên chi nhánh, Vui lòng chọn quyền khác" });
            }
            var result = await Mediator.Send(new UpdateProductRankCommand(req.ProductRanks, req.BranchId));

            if (result.IsSuccess)
            {
                return true;
            }

            return Failure(result.Errors);
        }

        public async Task<object> Post(AdjustProductUserReq req)
        {
            var adjustProductUserDto = Mapper.Map<List<AdjustProductUserModel>, List<AdjustProductUserDto>>(req.ProductUsers);

            if (adjustProductUserDto == null || !adjustProductUserDto.Any())
            {
                return Failure(new List<string>() { "Vui lòng kiểm tra lại dữ liệu đầu vào" });
            }

            var result = await Mediator.Send(new AdjustProductUserCommand(adjustProductUserDto));

            if (!result.IsSuccess)
            {
                return Failure(result.Errors);
            }

            await WriteAuditProductUserLog(
                result.Data.ProductUserListToAdd, 
                result.Data.OldProductUserList, 
                result.Data.ProductUserListToDelete, 
                result.Data.ProductUsersExisted, 
                result.Data.ProductIds);

            var productUserExisted = Mapper.Map<List<ProductUserDto>, List<ProductUserModel>>(result.Data.ProductUsersExisted);
            var newProductUsers = Mapper.Map<List<ProductUserDto>, List<ProductUserModel>>(result.Data.ProductUserListToAdd);

            var lstProductUserResult = new List<ProductUserModel>();
            lstProductUserResult.AddRange(productUserExisted ?? new List<ProductUserModel>());
            lstProductUserResult.AddRange(newProductUsers ?? new List<ProductUserModel>());

            try
            {
                await WriteLogProductUser(lstProductUserResult);
            }
            catch (Exception ex)
            {
                Log.Error(ex.Message, ex);
            }

            return true;
        }

        private async Task UpdateProductCost(UpdateProductCost req, 
            List<KiotVietFnB.Domain.AggregateModels.ProductAggregate.Models.Product> products, 
            List<KiotVietFnB.Domain.AggregateModels.BranchAggreate.Models.Branch> branches)
        {
            foreach (var branchId in req.BranchIds)
            {
                foreach (var product in req.Products)
                {
                    await UpdateProductCostUseAvgCost(req, branchId, product, products, branches, PosSettings.UseAvgCost);

                }
            }
        }

        private async Task UpdateProductCostUseAvgCost(UpdateProductCost req, int branchId, ProductUpdateCostDto productCost,
            List<KiotVietFnB.Domain.AggregateModels.ProductAggregate.Models.Product> products,
            List<KiotVietFnB.Domain.AggregateModels.BranchAggreate.Models.Branch> branches, bool useAvgCost)
        {
            try
            {
                var dateTimeNow = DateTime.Now;
                var result = await Mediator.Send(new GetLatestAvailableCostQuery()
                {
                    BeforeDate = useAvgCost ? req.BeforeDate : dateTimeNow,
                    BranchId = branchId,
                    ProductId = productCost.Id
                }
                );

                if (result.IsSuccess && string.IsNullOrEmpty(result.Data.DocumentCode) && productCost.Cost > 0)
                {
                    var productLog = products.Find(p => p.Id == productCost.Id);
                    var adjustmentDate = useAvgCost ? productLog.CreatedDate.AddSeconds(1) : dateTimeNow;
                    await ProductService.ChangeCostAsync(new CostTracking()
                    {
                        BranchId = branchId,
                        NewCost = productCost.Cost,
                        ProductId = productCost.Id,
                    }, adjustmentDate);
                    var branchLog = branches.Find(p => p.Id == branchId);
                    try
                    {
                        await AuditTrailLogService.AddLog(new AuditTrailLog
                        {
                            FunctionId = (int)FunctionType.Product,
                            Action = (int)AuditTrailAction.Update,
                            Content = $"Cập nhật giá vốn sản phẩm [ProductCode]{productLog?.Code}[/ProductCode] {productLog?.Name}.<br /> &nbsp; {branchLog?.Name} - giá vốn: {productCost.Cost} tại {adjustmentDate:MM/dd/yyyy HH:mm:ss}"
                        });
                    }
                    catch (Exception e)
                    {
                        Log.Error(e.Message, e);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        public async Task<object> Put(UpdatePriceBookListProduct req)
        {
            var priceBookeDetailDto = Mapper.Map<List<ProductPriceBookModel>, List<ProductPriceBookDto>>(req.ProductPriceBooks);

            var priceBookRemoves = req.ProductPriceBooksRemoves != null ? Mapper.Map<List<ProductPriceBookModel>, List<ProductPriceBookDto>>(req.ProductPriceBooksRemoves) : new List<ProductPriceBookDto>();

            var isUsingProductV2 = UnleashService.IsEnable(KiotVietFnB.Application.Driving.Ports.Constants.UnleashConst.UsingProductV2);
            
            var result = isUsingProductV2
                    ? await Mediator.Send(new KiotVietFnB.Application.Driving.Ports.Commands.ProductCommands.UpdatePriceBookListProductV2Command.UpdatePriceBookListProductCommand(priceBookeDetailDto, priceBookRemoves))
                    : await Mediator.Send(new UpdatePriceBookListProductCommand(priceBookeDetailDto, priceBookRemoves));

            if (!result.IsSuccess)
            {
                return Failure(result.Errors);
            }

            try
            {
                var data = result.Data;
                if (data == null)
                {
                    return true;
                }

                await WriteLogUpdatePriceBook(data.OldPriceBookDetails, data.NewPriceBookDetails, data.UpdatePriceBookDetails);
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }

            return true;
        }

        public async Task<object> Delete(DeleteProduct req)
        {
            var result = await Mediator.Send(new DeleteProductCommand(req.Id));

            if (result.IsSuccess)
            {
                await WriteLogDeleteProduct(result.Data);
                return true;
            }

            return Failure(result.Errors);
        }   


        private static object Failure(List<string> errors)
        {
            throw new KvException(errors.First() ?? KVMessage._GlobalErrorSummary);
        }

        private async Task<List<ProductImageDto>> ProcessProductImages()
        {
            if (Request.Files == null)
            {
                return null;
            }
            if (Request.Files.Count() > 5)
            {
                throw new KvValidateException("nhiều ảnh quá");
            }
            try
            {
                var s3Service = new AmazonS3Service(Globals.GetRetailerCode());
                var lsProductImages = new List<ProductImageDto>();
                foreach (var file in Request.Files)
                {
                    if (file == null || file.ContentLength <= 0) continue;
                    using (var stream = ImageUtils.GetThumbnailImage(file.InputStream, AppConfigInfo.AwsMaxPhotoWidth, AppConfigInfo.AwsMaxPhotoHeight, out string contentType))
                    {
                        var now = DateTime.Now;
                        var year = now.Year;
                        var month = now.Month.ToString().PadLeft(2, '0');
                        var key = $"{year}/{month}/{CurrentRetailerCode}/images/{Guid.NewGuid().ToString("N").ToLower()}";
                        if (!await s3Service.UploadFileAsync(AppConfigInfo.AwsBucketName, key, stream, contentType))
                            continue;

                        lsProductImages.Add(new ProductImageDto { Image = AppConfigInfo.AwsCloudFrontUrl + key });
                    }
                }
                return lsProductImages;
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
                return null;
            }
        }

        private static List<AttributeDto> GetListAttributeFilter(ProductListGet req)
        {
            // validate attribute filter
            List<AttributeDto> listAttributeFilter = new List<AttributeDto>();
            if (!string.IsNullOrEmpty(req.AttributeFilter))
            {
                listAttributeFilter = JsonConvert.DeserializeObject<List<AttributeDto>>(req.AttributeFilter);
            }

            return listAttributeFilter;
        }

        private static void MapImageForProducts(List<ProductDto> products, List<ProductImageDto> productImages)
        {
            if (productImages == null || !productImages.Any())
            {
                return;
            }

            if (products == null || !products.Any())
            {
                return;
            }

            products.ForEach(product =>
            {
                product.ProductImages = productImages.Select(x => new ProductImageDto
                {
                    Image = x.Image
                }).ToList();
            });
        }

        #region Audit Log
        private async Task WriteLogUpdateListOnHand(List<ProductHistoryUpdateOnHandDto> productHistoryUpdateOnHands)
        {
            if (productHistoryUpdateOnHands == null || !productHistoryUpdateOnHands.Any())
            {
                return;
            }

            try
            {
                var branch = await BranchRepository.GetAsync(CurrentBranchId);

                foreach (var productHistoryUpdateOnHand in productHistoryUpdateOnHands)
                {
                    var oldOnHand = productHistoryUpdateOnHand.OldProductOnHand ?? new ProductOnHandDto();
                    var newOnHand = productHistoryUpdateOnHand.ProductOnHand;

                    if (newOnHand == null) 
                    {
                        continue;
                    }

                    if (oldOnHand.OnHand == newOnHand.OnHand && oldOnHand.MinQuantity == newOnHand.MinQuantity && oldOnHand.MaxQuantity == newOnHand.MaxQuantity)
                    {
                        continue;
                    }

                    var stockTakeInfo = productHistoryUpdateOnHand.StockTake;

                    var product = productHistoryUpdateOnHand.Product;

                    var contentBuilder = BuildContentOnHandDiff(oldOnHand, newOnHand);
                    
                    contentBuilder.Insert(0, $"Chi nhánh: {branch?.Name}");
                    contentBuilder.Insert(0, $"Cập nhật thông tin Tồn kho sản phẩm [ProductCode]{product.Code}[/ProductCode] {product.FullName}<br />");
                    await AuditTrailLogService.AddLog(new AuditTrailLog
                    {
                        FunctionId = (int)FunctionType.Product,
                        Action = (int)AuditTrailAction.Update,
                        Content = contentBuilder.ToString()
                    });

                    if (stockTakeInfo != null)
                        await AuditTrailLogService.AddLog(new AuditTrailLog
                        {
                            FunctionId = (int)FunctionType.StockTake,
                            Action = (int)AuditTrailAction.Create,
                            Content = $@"Tạo phiếu kiểm kho: [StockTakeCode]{stockTakeInfo.Code}[/StockTakeCode], ngày cân bằng kho: {DateFormat(stockTakeInfo.AdjustmentDate ?? default(DateTime))}<br />"
                        });
                }
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private static StringBuilder BuildContentOnHandDiff(ProductOnHandDto oldOnHand, ProductOnHandDto newOnHand)
        {
            var contentBuilder = new StringBuilder();
            if (oldOnHand.OnHand != newOnHand.OnHand)
            {
                contentBuilder.Append($"<br /> &nbsp; - Tồn kho: {Normallize(oldOnHand.OnHand)} >> {Normallize(newOnHand.OnHand)}");
            }
            if (oldOnHand.MinQuantity != newOnHand.MinQuantity)
            {
                contentBuilder.Append($"<br /> &nbsp; - Ít nhất: {Normallize(oldOnHand.MinQuantity)} >> {Normallize(newOnHand.MinQuantity)}");
            }
            if (oldOnHand.MaxQuantity != newOnHand.MaxQuantity)
            {
                contentBuilder.Append($"<br /> &nbsp; - Nhiều nhất: {Normallize(oldOnHand.MaxQuantity)} >> {Normallize(newOnHand.MaxQuantity)}");
            }

            return contentBuilder;
        }

        private async Task WriteLogUpdateListCostProductForCurrentBranch(List<ProductUpdateCostDto> products)
        {
            try
            {
                var currentDate = DateTime.Now;
                var branchResult = await Mediator.Send(new GetBranchByIdQuery(CurrentBranchId));
                if (!branchResult.IsSuccess)
                {
                    Log.Error(branchResult.Errors);
                    return;
                }

                foreach (var item in products ?? new List<ProductUpdateCostDto>())
                {
                    await AuditTrailLogService.AddLog(new AuditTrailLog
                    {
                        FunctionId = (int)FunctionType.Product,
                        Action = (int)AuditTrailAction.Update,
                        Content = $"Cập nhật giá vốn sản phẩm [ProductCode]{item.Code}[/ProductCode] {item.FullName}.<br /> &nbsp; {branchResult.Data?.Name} - giá vốn: {item.Cost} tại {currentDate:MM/dd/yyyy HH:mm:ss}"
                    });
                }
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task WriteLogProductSameType(List<ProductDto> products, List<ProductCreateToppingModel> productToppings = null)
        {
            try
            {
                if (products == null || !products.Any())
                {
                    return;
                }

                var priceBookInfo = await GetListPriceBookByProducts(products);
                var attributeInfo = await GetListAttributeByProducts(products);

                var categoryId = products.Where(x => x.CategoryId != null && x.CategoryId > 0).Select(x => x.CategoryId ?? 0).FirstOrDefault();
                var logSaleOnBranch = await GenerateLogProductSaleOnBranch(products);
                var logCategory = (await Mediator.Send(new FnGetReserveHieranchyCategoryStrQuery { CategoryId = categoryId, Delimiter = " >> " })).Data;

                foreach (var item in products)
                {
                    var lstProductToppings = productToppings?.Where(x => x.ProductId == item.Id).ToList();
                    var productFormulas = await GenerateLogProductFormulas(item.ProductFormulas, item);
                    var logTopping = await GenerateLogTopping(lstProductToppings, item.IsTopping);
                    var logCreate = GenerateLogCreateProduct(item, logTopping, attributeInfo, logCategory, logSaleOnBranch, productFormulas, string.Empty);
                    await AuditTrailLogService.AddLog(logCreate);

                    if (priceBookInfo.Any())
                    {
                        var logPriceBook = GenerateLogPriceBook(item, priceBookInfo);
                        await AuditTrailLogService.AddLog(logPriceBook);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task<List<Attribute>> GetListAttributeByProducts(List<ProductDto> products)
        {
            var productAttributes = new List<ProductAttributeDto>();

            var productsWrteLog = products.Where(x => x.ProductAttributes != null && x.ProductAttributes.Count > 0).ToList();
            if (!productsWrteLog.Any())
            {
                return new List<Attribute>();
            }

            productsWrteLog.ForEach(item =>
            {
                if (item.ProductAttributes != null && item.ProductAttributes.Any())
                {
                    productAttributes.AddRange(item.ProductAttributes ?? new List<ProductAttributeDto>());
                }
            });

            if (!productAttributes.Any())
            {
                return new List<Attribute>();
            }

            var attributeIds = productAttributes.Where(x => x.AttributeId != null && x.AttributeId > 0).GroupBy(x => x.AttributeId).Select(p => p.Key ?? 0).ToList();
            if (!attributeIds.Any())
            {
                return new List<Attribute>();
            }

            return await AttributeRepository.GetByIdsAsync(attributeIds);
        }

        private async Task<List<PriceBook>> GetListPriceBookByProducts(List<ProductDto> products)
        {
            var priceBookDetails = new List<PriceBookDetailDto>();

            var productsWriteLog = products.Where(x => x.PriceBookDetails != null && x.PriceBookDetails.Count > 0).ToList();
            if (!productsWriteLog.Any())
            {
                return new List<PriceBook>();
            }

            productsWriteLog.ForEach(item =>
            {
                if (item.PriceBookDetails != null && item.PriceBookDetails.Any())
                {
                    priceBookDetails.AddRange(item.PriceBookDetails ?? new List<PriceBookDetailDto>());
                }
            });

            if (!priceBookDetails.Any())
            {
                return new List<PriceBook>();
            }

            var pricebookIds = priceBookDetails.Where(x => x.PriceBookId != null && x.PriceBookId > 0).GroupBy(x => x.PriceBookId).Select(p => p.Key ?? 0).ToList();

            if (!pricebookIds.Any())
            {
                return new List<PriceBook>();
            }

            return await PriceBookRepository.GetListAsync(new PriceBookByIdsSpecification(pricebookIds));
        }

        private async Task WriteLogCreateProduct(List<ProductDto> productsResult, ProductCreateDto reqProduct)
        {
            try
            {
                var priceBookInfo = new List<PriceBook>();
                if (reqProduct.PriceBooks?.Any() == true)
                {
                    var pricebookIds = reqProduct.PriceBooks.Select(p => p.PriceBookId ?? 0).ToList();
                    priceBookInfo = await PriceBookRepository.GetListAsync(new PriceBookByIdsSpecification(pricebookIds));
                }

                var attributeInfo = new List<Attribute>();
                if (reqProduct.ProductAttributes?.Any() == true)
                {
                    var attributeIds = reqProduct.ProductAttributes.Select(p => p.AttributeId).ToList();
                    attributeInfo = await AttributeRepository.GetByIdsAsync(attributeIds);
                }

                var logSaleOnBranch = await GenerateLogProductSaleOnBranch(productsResult);
                var logCategory = (await Mediator.Send(new FnGetReserveHieranchyCategoryStrQuery { CategoryId = reqProduct.CategoryId, Delimiter = " >> " })).Data;
                var logTopping = await GenerateLogTopping(reqProduct);
                var logProductTax = await GenerateLogProductTax(reqProduct.TaxId ?? 0);
                var productFormulas = await GenerateLogProductFormulas(reqProduct.ProductFormulas, productsResult.FirstOrDefault());
                foreach (var item in productsResult)
                {
                    if (priceBookInfo.Any())
                    {
                        var logPriceBook = GenerateLogPriceBook(item, reqProduct, priceBookInfo);
                        await AuditTrailLogService.AddLog(logPriceBook);
                    }

                    var logCreate = GenerateLogCreateProduct(item, logTopping, attributeInfo, logCategory, logSaleOnBranch, productFormulas, logProductTax);
                    await AuditTrailLogService.AddLog(logCreate);
                }
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task WriteLogUpdateBasicInfoProduct(KiotVietFnB.Domain.AggregateModels.ProductAggregate.Models.Product oldProd, ProductDto newProd, decimal oldCost)
        {
            try
            {
                var contentBuilder = new StringBuilder();
                if (oldProd.Name != newProd.Name) contentBuilder.Append($"&nbsp; - Tên sản phẩm: {oldProd.Name} >> {newProd.Name}<br />");
                if (oldProd.Code != newProd.Code) contentBuilder.Append($"&nbsp; - Mã sản phẩm: {oldProd.Code} >> {newProd.Code}<br />");
                if (oldProd.BasePrice != newProd.BasePrice) contentBuilder.Append($" &nbsp; - Giá bán: {NormallizeWfp(oldProd.BasePrice)} >> {NormallizeWfp(newProd.BasePrice)}<br />");
                if (oldCost != newProd.Cost) contentBuilder.Append($" &nbsp; - Giá vốn: {NormallizeWfp(oldCost)} >> {NormallizeWfp(newProd.Cost)}<br />");
                if (oldProd.CategoryId != newProd.CategoryId)
                {
                    var categories = await CategoryRepository.GetByIdsAsync(new List<int> { oldProd.CategoryId, newProd.CategoryId.Value });
                    contentBuilder.Append($" &nbsp; - Nhóm hàng: {categories.FirstOrDefault(p => p.Id == oldProd.CategoryId)?.Name} >> {categories.FirstOrDefault(p => p.Id == newProd.CategoryId)?.Name}<br />");
                }
                if (oldProd.ProductGroup != newProd.ProductGroup)
                {
                    var oldType = KiotVietFnB.Domain.AggregateModels.ProductAggregate.Enums.ProductGroupType.From(oldProd?.ProductGroup ?? 3).Description;
                    var newType = KiotVietFnB.Domain.AggregateModels.ProductAggregate.Enums.ProductGroupType.From(newProd?.ProductGroup ?? 0).Description;
                    contentBuilder.Append($" &nbsp; - Loại thực đơn: {oldType} >> {newType}<br />");
                }
                if (oldProd?.ProductType != newProd?.ProductType)
                {
                    var oldType = KiotVietFnB.Domain.AggregateModels.ProductAggregate.Enums.ProductTypes.From(oldProd?.ProductType ?? 0).Description;
                    var newType = KiotVietFnB.Domain.AggregateModels.ProductAggregate.Enums.ProductTypes.From(newProd?.ProductType ?? 0).Description;
                    contentBuilder.Append($" &nbsp; - Loại hàng hóa: {oldType} >> {newType}<br />");
                }
                if (oldProd?.AllowsSale != newProd?.AllowsSale)
                {
                    contentBuilder.Append($" &nbsp; - Bán trực tiếp: {BooleanToYesNo(oldProd?.AllowsSale)} >> {BooleanToYesNo(newProd?.AllowsSale)}<br />");
                }
                if (oldProd?.IsRewardPoint != newProd?.IsRewardPoint)
                {
                    contentBuilder.Append($" &nbsp; - Tích điểm: {BooleanToYesNo(oldProd?.IsRewardPoint)} >> {BooleanToYesNo(newProd?.IsRewardPoint)}<br />");
                }
                if (oldProd?.IsTopping != newProd?.IsTopping)
                {
                    contentBuilder.Append($" &nbsp; - Là món thêm: {BooleanToYesNo(oldProd?.IsTopping)} >> {BooleanToYesNo(newProd?.IsTopping)}<br />");
                }

                if (contentBuilder.Length > 0)
                {
                    contentBuilder.Insert(0, $"Cập nhật thông tin cơ bản của sản phẩm [ProductCode]{oldProd?.Code}[/ProductCode] {oldProd?.FullName}<br />");
                    await AuditTrailLogService.AddLog(new AuditTrailLog
                    {
                        FunctionId = (int)FunctionType.Product,
                        Action = (int)AuditTrailAction.Update,
                        Content = contentBuilder.ToString()
                    });
                }
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }


        private async Task WriteLogUpdateProductFormula(ProductDto productResult, ProductUpdateFormulaDto reqProduct)
        {
            try
            {
                var productFormulas = await GenerateLogProductFormulas(reqProduct.ProductFormulas, productResult);
                var logCreate = GenerateLogUpdateProductFormulas(productResult, productFormulas);
                await AuditTrailLogService.AddLog(logCreate);
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task WriteLogUpdateImage(ProductDto product)
        {
            try
            {
                await AuditTrailLogService.AddLog(new AuditTrailLog
                {
                    FunctionId = (int)FunctionType.Product,
                    Action = (int)AuditTrailAction.Update,
                    Content = $"Cập nhật thông tin Hình ảnh sản phẩm [ProductCode]{product.Code}[/ProductCode] {product.FullName}."
                });
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task WriteLogUpdateUnitName(ProductDto product, string oldUnit)
        {
            try
            {
                await AuditTrailLogService.AddLog(new AuditTrailLog
                {
                    FunctionId = (int)FunctionType.Product,
                    Action = (int)AuditTrailAction.Update,
                    Content = $"Cập nhật thông tin Đơn vị tính sản phẩm [ProductCode]{product.Code}[/ProductCode] {product.Name}.<br /> &nbsp; - Đơn vị tính: {oldUnit} >> {product.Unit}"
                });
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }
  
        private async Task WriteLogAddProductUnit(KiotVietFnB.Domain.AggregateModels.ProductAggregate.Models.Product oldProd, List<ProductDto> newProducts)
        {
            try
            {
                var newUnit = newProducts.FirstOrDefault(p => p.Id == oldProd.Id)?.Unit ?? string.Empty;
                var contentLog = new StringBuilder($"Cập nhật thông tin Đơn vị tính sản phẩm [ProductCode]{oldProd.Code}[/ProductCode] {oldProd.Name}.<br /> &nbsp; - Đơn vị tính: {oldProd.Unit} >> {newUnit}<br />");

                if (newProducts.Count > 1)
                {
                    contentLog.Append("Thêm mới hàng hóa cùng loại: <br />");
                    foreach (var item in newProducts.Where(p => p.Id != oldProd.Id))
                    {
                        contentLog.Append($" &nbsp; - [ProductCode]{item.Code}[/ProductCode]  {item.FullName}<br />");
                    }
                }

                await AuditTrailLogService.AddLog(new AuditTrailLog
                {
                    FunctionId = (int)FunctionType.Product,
                    Action = (int)AuditTrailAction.Update,
                    Content = contentLog.ToString()
                });
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task WriteLogUpdateTopping(ProductDto product, ProductUpdateDto request, List<ProductTopping> oldToppings)
        {
            try
            {
                List<long> addIds, delIds = new List<long>();

                var validIds = request.ProductToppings.Select(p => p.ProductId);
                if (product.IsTopping == true)
                {
                    var productIds = oldToppings.Select(p => p.ProductId).ToList();
                    delIds = oldToppings.Where(p => !validIds.Contains(p.ProductId)).Select(p => p.ProductId).ToList();
                    addIds = validIds.Except(productIds).ToList();
                }
                else
                {
                    var toppingIds = oldToppings.Select(p => p.ToppingId).ToList();
                    delIds = oldToppings.Where(p => !validIds.Contains(p.ToppingId)).Select(p => p.ToppingId).ToList();
                    addIds = validIds.Except(toppingIds).ToList();
                }

                var allToppingIds = delIds.Concat(addIds).ToList();
                var typeDish = product.IsTopping == true ? "Hàng sử dụng món thêm" : "Món thêm";
                var contentBuilder = new StringBuilder($"Cập nhật thông tin {typeDish} sản phẩm [ProductCode]{product.Code}[/ProductCode] {product.FullName}<br />");
                var toppingsInfo = await ProductRepository.GetByIdsAsync(allToppingIds);

                int addCount = addIds.Count;
                if (addCount > 0)
                {
                    contentBuilder.Append($" &nbsp; - Thêm mới({addCount}): ");
                    var fullnames = String.Join(", ", toppingsInfo.Where(p => addIds.Contains(p.Id)).Select(p => $"[ProductCode]{p.Code}[/ProductCode] {p.FullName}"));
                    contentBuilder.Append(fullnames);
                    if (addCount > 50)
                        contentBuilder.Append(", ...");
                    contentBuilder.Append("<br />");
                }

                int delCount = delIds.Count;
                if (delCount > 0)
                {
                    contentBuilder.Append($" &nbsp; - Xóa({delCount}): ");
                    var fullnames = String.Join(", ", toppingsInfo.Where(p => delIds.Contains(p.Id)).Select(p => $"[ProductCode]{p.Code}[/ProductCode] {p.FullName}"));
                    contentBuilder.Append(fullnames);
                    if (delCount > 50)
                        contentBuilder.Append(", ...");
                    contentBuilder.Append("<br />");
                }

                await AuditTrailLogService.AddLog(new AuditTrailLog
                {
                    FunctionId = (int)FunctionType.Product,
                    Action = (int)AuditTrailAction.Update,
                    Content = contentBuilder.ToString()
                });
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task WriteLogUpdatePriceBook(List<PriceBookDetailDto> oldPriceBookDetails, List<PriceBookDetailDto> newPriceBookDetails, List<PriceBookDetailDto> updatePriceBookDetails)
        {
            try
            {
                var contentBuilder = new StringBuilder();
                contentBuilder.Append("Cập nhật thông tin Bảng giá sản phẩm:<br />");
                if (newPriceBookDetails.Any())
                {
                    var priceBookName = newPriceBookDetails.FirstOrDefault()?.PriceBookName;
                    contentBuilder.Append($"Thêm mới {priceBookName}:<br />");
                    foreach (var item in newPriceBookDetails)
                    {
                        contentBuilder.Append($"&nbsp; - [ProductCode]{item.ProductCode}[/ProductCode] {item.ProductName}: {Normallize(item.Price)}<br />");
                    }
                }

                if (updatePriceBookDetails.Any())
                {
                    var updatelist = new List<string>();
                    foreach (var priceBookInfo in updatePriceBookDetails)
                    {
                        var oldPrice = oldPriceBookDetails.FirstOrDefault(p => p.PriceBookId == priceBookInfo.PriceBookId);
                        if (oldPrice?.Price != priceBookInfo.Price)
                        {
                            updatelist.Add($"&nbsp; - [ProductCode]{priceBookInfo.ProductCode}[/ProductCode] {priceBookInfo.ProductName}: {Normallize(oldPrice?.Price ?? 0)} >> {Normallize(priceBookInfo.Price)}<br />");
                        }
                    }
                    if (updatelist.Any())
                    {
                        var priceBookName = updatePriceBookDetails.FirstOrDefault()?.PriceBookName;
                        contentBuilder.Append($"Cập nhật {priceBookName}:<br /> {String.Join("", updatelist)}");
                    }
                }

                if (newPriceBookDetails.Any() || updatePriceBookDetails.Any())
                {
                    await AuditTrailLogService.AddLog(new AuditTrailLog
                    {
                        FunctionId = (int)FunctionType.Product,
                        Action = (int)AuditTrailAction.Update,
                        Content = contentBuilder.ToString()
                    });
                }    
                   
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task WriteLogUpdatePriceBook(long productId, List<PriceBookDetailDto> priceBookDetailDtos, List<PriceBookDetail> oldPriceBookDetail)
        {
            try
            {
                var product = await ProductRepository.GetAsync(productId);
                var contentBuilder = new StringBuilder($"Cập nhật thông tin Bảng giá sản phẩm [ProductCode]{product.Code}[/ProductCode] {product.FullName}<br />");
                var allPriceBookIds = priceBookDetailDtos.Select(p => p.PriceBookId ?? 0).ToList();
                var priceBooksInfo = await PriceBookRepository.GetListAsync(new PriceBookByIdsSpecification(allPriceBookIds));
                var oldPriceBookIds = oldPriceBookDetail.Select(p => p.PriceBookId);

                var newPriceBookDetails = priceBookDetailDtos.Where(p => !oldPriceBookIds.Contains(p.PriceBookId ?? 0));
                if (newPriceBookDetails.Any())
                {
                    contentBuilder.Append("Thêm mới:<br />");
                    foreach (var item in newPriceBookDetails)
                    {
                        var priceBookInfo = priceBooksInfo.FirstOrDefault(p => p.Id == item.PriceBookId);
                        contentBuilder.Append($"&nbsp; - {priceBookInfo?.Name}: {Normallize(item.Price)}<br />");
                    }
                }

                var updatePriceBookDetail = priceBookDetailDtos.Where(p => !newPriceBookDetails.Select(x => x.PriceBookId).Contains(p.PriceBookId)).ToList();
                if (updatePriceBookDetail.Count > 0)
                {
                    var updatelist = new List<string>();
                    foreach (var update in updatePriceBookDetail)
                    {
                        var oldPrice = oldPriceBookDetail.FirstOrDefault(p => p.PriceBookId == update.PriceBookId);
                        if (oldPrice.Price != update.Price)
                        {
                            var priceBookInfo = priceBooksInfo.FirstOrDefault(p => p.Id == update.PriceBookId);
                            updatelist.Add($"&nbsp; - {priceBookInfo?.Name}: {Normallize(oldPrice.Price)} >> {Normallize(update.Price)}<br />");
                        }
                    }
                    if (updatelist.Any())
                    {
                        contentBuilder.Append($"Cập nhật:<br /> {String.Join("", updatelist)}");
                    }
                }

                if (newPriceBookDetails.Any() || updatePriceBookDetail.Count > 0)
                    await AuditTrailLogService.AddLog(new AuditTrailLog
                    {
                        FunctionId = (int)FunctionType.Product,
                        Action = (int)AuditTrailAction.Update,
                        Content = contentBuilder.ToString()
                    });
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task WriteLogUpdateOnHand(long productId, ProductBranch oldOnHand, ProductUpdateDto request, StockTake stockTakeInfo)
        {
            try
            {
                var contentBuilder = new StringBuilder();
                if (oldOnHand.OnHand != request.OnHand)
                {
                    contentBuilder.Append($"<br /> &nbsp; - Tồn kho: {Normallize(oldOnHand.OnHand)} >> {Normallize(request.OnHand)}");
                }
                if (oldOnHand.MinQuantity != request.MinQuantity)
                {
                    contentBuilder.Append($"<br /> &nbsp; - Ít nhất: {Normallize(oldOnHand.MinQuantity)} >> {Normallize(request.MinQuantity)}");
                }
                if (oldOnHand.MaxQuantity != request.MaxQuantity)
                {
                    contentBuilder.Append($"<br /> &nbsp; - Nhiều nhất: {Normallize(oldOnHand.MaxQuantity)} >> {Normallize(request.MaxQuantity)}");
                }

                if (oldOnHand.OnHand != request.OnHand || oldOnHand.MinQuantity != request.MinQuantity || oldOnHand.MaxQuantity != request.MaxQuantity)
                {
                    var product = await ProductRepository.GetAsync(productId);
                    var branch = await BranchRepository.GetAsync(CurrentBranchId);
                    contentBuilder.Insert(0, $"Chi nhánh: {branch?.Name}");
                    contentBuilder.Insert(0, $"Cập nhật thông tin Tồn kho sản phẩm [ProductCode]{product.Code}[/ProductCode] {product.FullName}<br />");
                    await AuditTrailLogService.AddLog(new AuditTrailLog
                    {
                        FunctionId = (int)FunctionType.Product,
                        Action = (int)AuditTrailAction.Update,
                        Content = contentBuilder.ToString()
                    });

                    if (stockTakeInfo != null)
                        await AuditTrailLogService.AddLog(new AuditTrailLog
                        {
                            FunctionId = (int)FunctionType.StockTake,
                            Action = (int)AuditTrailAction.Create,
                            Content = $@"Tạo phiếu kiểm kho: [StockTakeCode]{stockTakeInfo.Code}[/StockTakeCode], ngày cân bằng kho: {DateFormat(stockTakeInfo.AdjustmentDate ?? default(DateTime))}<br />
                                 &nbsp; - [ProductCode]{product.Code}[/ProductCode] : {Normallize(stockTakeInfo.StockTakeDetails?.FirstOrDefault()?.ActualCount)}/{Normallize(stockTakeInfo?.StockTakeDetails?.FirstOrDefault().SystemCount)}<br>"
                        });
                }
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }

        private async Task WriteLogDeleteProduct(List<ProductDto> productsDeleted)
        {
            try
            {
                var content = string.Empty;
                if (productsDeleted.Count == 1)
                {
                    content += $"Xóa thông tin hàng hóa mã: {productsDeleted[0].Code}, tên: {productsDeleted[0].Name}";
                }
                else if (productsDeleted.Count > 1)
                {
                    content += "Xóa thông tin hàng hóa mã:<br><div>";
                    foreach (var item in productsDeleted)
                    {
                        content += $" - {item.Code}, tên: {item.Name} <br>";
                    }
                    content += "</div>";
                }
                await AuditTrailLogService.AddLog(new AuditTrailLog
                {
                    FunctionId = (int)FunctionType.Product,
                    Action = (int)AuditTrailAction.Delete,
                    Content = content
                });
            } catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }
        
        private async Task WriteLogUpdateAttribute(List<ProductDto> beforProducts, List<ProductDto> afterProducts)
        {
            try
            {
                var logs = new List<AuditTrailLog>();
                foreach (var afterProd in afterProducts)
                {
                    var beforProd = beforProducts.Find(p => p.Id == afterProd.Id);
                    var log = new AuditTrailLog
                    {
                        FunctionId = (int)FunctionType.Product,
                        Action = (int)AuditTrailAction.Update,
                        Content = $"Cập nhật thông tin thuộc tính của sản phẩm: [ProductCode]{beforProd.Code}[/ProductCode], tên: {beforProd.Name} "
                    };
                    
                    string subContent = string.Empty;
                    foreach (var afterAttr in afterProd.ProductAttributes)
                    {
                        var beforAttr = beforProd.ProductAttributes.FirstOrDefault(p => p.AttributeId == afterAttr.AttributeId);
                        var attrInfo = await AttributeRepository.GetAsync((long)afterAttr.AttributeId);
                        if ( beforAttr == null)
                        {
                            subContent += $"<br /> &emsp; - Thêm mới loại thuộc tính: {attrInfo.Name}, giá trị: {afterAttr.AttributeValue}";
                            continue;
                        }
                        
                        if (beforAttr.AttributeValue != afterAttr.AttributeValue)
                        {
                            subContent += $"<br /> &emsp; - Sửa loại thuộc tính: {attrInfo.Name}, giá trị: {beforAttr.AttributeValue} -> {afterAttr.AttributeValue}";
                        }
                    }
                    if (!string.IsNullOrEmpty(subContent))
                    {
                        log.Content += subContent;
                        logs.Add(log);
                    }
                }

                foreach (var log in logs)
                {
                    await AuditTrailLogService.AddLog(log);
                }
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }
        }


        private AuditTrailLog GenerateLogCreateProduct(ProductDto product, string logTopping, List<Attribute> attributeInfo, string logCategory, Dictionary<long, string> logSaleOnBranch, (string, decimal) productFormulasLog, string logProductTax)
        {
            var contentFormat = @"Thêm mới sản phẩm 
                                    <br />Mã: [ProductCode]{0}[/ProductCode]
                                    <br />Tên: {1}
                                    <br />Đơn vị tính: {6}
                                    <br />Nhóm hàng: {7}
                                    <br />Giá bán: {2}
                                    <br />Giá vốn: {3}{15}{16}{4}{10}{5}{14}{8}{9}{11}
                                    <br />Quản lý tồn kho: {12}
                                    {13}
                                    <br /> {17}";
            var content = String.Format(contentFormat
                , product.Code // 0
                , product.Name // 1
                , NormallizeWfp((double)product.BasePrice) // 2
                , string.IsNullOrEmpty(productFormulasLog.Item1) ? GenerateLogProductCost(product) : NormallizeWfp(productFormulasLog.Item2) // 3
                , GenerateLogProductOnHand(product) // 4
                , productFormulasLog.Item1  //5
                , GenerateLogProductUnit(product) // 6
                , logCategory // 7
                , GenerateLogProductAllowsSale(product) + GenerateLogProductRewardPoint(product) // 8
                , GenerateLogProductAttr(product, attributeInfo) //9
                , "" // shelves 10                                                 
                , "" // productSaleForBranchContent 11
                , GenerateLogProductOnHandManagement(product) //12
                , logTopping // 13
                , logProductTax // Tax //14
                , GenerateLogProductIsTimetype(product) //15
                , GenerateLogProductIsTopping(product) //16
                , logSaleOnBranch[product.Id.Value] // 17
                );
            return new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Product,
                Action = (int)AuditTrailAction.Create,
                Content = content
            };
        }

        private AuditTrailLog GenerateLogUpdateProductFormulas(ProductDto product, (string, decimal) productFormulasLog)
        {
            var contentFormat = @"Cập nhật sản phẩm 
                                    <br />Mã: [ProductCode]{0}[/ProductCode]
                                    <br />Tên: {1}
                                    <br />Giá vốn: {2}{3}";
            var content = String.Format(contentFormat
              , product.Code // 0
              , product.Name // 1
              , NormallizeWfp(productFormulasLog.Item2) // 2
              , productFormulasLog.Item1  //3
              );
            return new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Product,
                Action = (int)AuditTrailAction.Create,
                Content = content
            };
        }


        private static string GenerateLogProductAllowsSale(ProductDto product)
        {
            return $"<br />Được bán trực tiếp: {BooleanToYesNo(product.AllowsSale)}";
        }

        private static string GenerateLogProductIsTopping(ProductDto product)
        {
            return $"<br />Là món thêm: {BooleanToYesNo(product.IsTopping)}";
        }

        private static string GenerateLogProductIsTimetype(ProductDto product)
        {
            return $"<br />Là hàng hóa tính giờ: {BooleanToYesNo(product.IsTimeType)}";
        }

        private static string GenerateLogProductRewardPoint(ProductDto product)
        {
            return $"<br /> Tích điểm: {BooleanToYesNo(product.IsRewardPoint)}";
        }

        private static string GenerateLogProductUnit(ProductDto product)
        {
            return !string.IsNullOrEmpty(product.Unit) ? $"{product.Unit}" : "";
        }

        private string GenerateLogProductAttr(ProductDto productResult, List<Attribute> attributeInfo)
        {
            if (productResult.ProductAttributes?.Any() != true) return String.Empty;

            var logAttr = new StringBuilder();
            logAttr.Append("<br />Thuộc tính bao gồm:");
            foreach (var itemProductAttr in productResult.ProductAttributes)
            {
                var attr = attributeInfo.FirstOrDefault(p => p.Id == itemProductAttr.AttributeId);
                logAttr.Append($"<br /> &emsp; - {attr?.Name}: {itemProductAttr.AttributeValue}");
            }
            return logAttr.ToString();
        }

        private static string GenerateLogProductOnHand(ProductDto product)
        {
            return (product.ProductType == (byte)ProductType.Purchased)
                ? $"<br />Tồn kho: {Normallize(product.OnHand)}"
                : "";
        }

        /// <summary>
        /// Quản lý tồn kho
        /// </summary>
        private static string GenerateLogProductOnHandManagement(ProductDto product)
        {
            return BooleanToYesNo(product.InventoryTrackingIgnore);
        }

        private static string GenerateLogProductCost(ProductDto product)
        {
            return NormallizeWfp(product.Cost);
        }

        private async Task<string> GenerateLogTopping(List<ProductCreateToppingModel> productToppings, bool? isTopping)
        {
            if (productToppings?.Any() != true) return string.Empty;

            var toppingIds = productToppings.Take(50).Select(p => p.ProductId).ToList();
            var toppingsInfo = await ProductRepository.GetByIdsAsync(toppingIds);
            var toppingLog = String.Join(", ", toppingsInfo.Select(p => $"[ProductCode]{p.Code}[/ProductCode] {p.FullName}"));

            var typeDish = isTopping == true ? "Món thêm" : "Hàng sử dụng món thêm";
            var log = @"<br />{0}:<br /> &emsp; - Số lượng {1}: {2} ";
            var quantity = productToppings.Count;
            if (quantity > 50)
                log += "...";
            return string.Format(log, typeDish, quantity, toppingLog);
        }

        private async Task<string> GenerateLogTopping(ProductCreateDto reqProduct)
        {
            if (reqProduct.ProductToppings?.Any() != true) return String.Empty;

            var toppingIds = reqProduct.ProductToppings.Take(50).Select(p => p.ProductId).ToList();
            var toppingsInfo = await ProductRepository.GetByIdsAsync(toppingIds);
            var toppingLog = String.Join(", ", toppingsInfo.Select(p => $"[ProductCode]{p.Code}[/ProductCode] {p.FullName}"));

            var typeDish = reqProduct.IsTopping == true ? "Món thêm" : "Hàng sử dụng món thêm";
            var log = @"<br />{0}:<br /> &emsp; - Số lượng {1}: {2} ";
            var quantity = reqProduct.ProductToppings.Count;
            if (quantity > 50)
                log += "...";
            return string.Format(log, typeDish, quantity, toppingLog);
        }
        private async Task<string> GenerateLogProductTax(long taxId)
        {
            if (taxId <= 0) return string.Empty;
            var taxes = await Mediator.Send(new FetchTaxByModifiedDateQuery(null));

            if (taxes.IsSuccess)
            {
                var taxInfo = taxes.Data.FirstOrDefault(t => t.Id == taxId);

                if (taxInfo != null)
                {
                    return $"<br />Thuế: {taxInfo.Name} ({taxInfo.TaxRate}%)";
                }
            }

            return string.Empty;
        }

        private async Task<(string, decimal)> GenerateLogProductFormulas(List<ProductFormulaDto> productFormulas, ProductDto productResult)
        {
            if (productFormulas?.Any() != true) return (string.Empty, 0);
            decimal totalCost = 0;
            var productMaterials = string.Empty;
            if (productResult.ProductType != (byte)ProductType.Service)
            {
                var materialIds = productFormulas.Take(50).Select(p => p.MaterialId).ToList();
                var products = await ProductRepository.GetByIdsAsync(materialIds);
                var productBranches = await ProductBranchRepository.GetByBranchIdAndProductIds(CurrentBranchId, materialIds);
                if (productResult.ProductFormulaHistoryId > 0)
                {
                    productMaterials += $", Mã lịch sử thành phần: {productResult.ProductFormulaHistoryId}, bao gồm:<div>";
                }
                else
                {
                    productMaterials += ", hàng thành phần bao gồm:<div>";
                }

                foreach (var productFormula in productFormulas)
                {
                    var material = products.FirstOrDefault(m => m.Id == productFormula.MaterialId);
                    var cost = productBranches.FirstOrDefault(m => m.ProductId == productFormula.MaterialId).Cost;
                    totalCost += cost * productFormula.Quantity;
                    productMaterials += $"- [ProductCode]{material.Code}[/ProductCode] {material.FullName} : {Normallize(productFormula.Quantity)}*{NormallizeWfp((double)cost)}<br>";
                }
                productMaterials += "</div>";
            }

            return (productMaterials, totalCost);
        }

        private async Task<(string, decimal)> GenerateLogProductFormulas(List<ProductFormulaUpdateDto> productFormulas, ProductDto productResult)
        {
            if (productFormulas?.Any() != true) return (string.Empty, 0);
            decimal totalCost = 0;
            var productMaterials = string.Empty;
            if (productResult.ProductType != (byte)ProductType.Service)
            {
                var materialIds = productFormulas.Take(50).Select(p => p.MaterialId).ToList();
                var products = await ProductRepository.GetByIdsAsync(materialIds);
                var productBranches = await ProductBranchRepository.GetByBranchIdAndProductIds(CurrentBranchId, materialIds);
                if (productResult.ProductFormulaHistoryId > 0)
                {
                    productMaterials += $", Mã lịch sử thành phần: {productResult.ProductFormulaHistoryId}, bao gồm:<div>";
                }
                else
                {
                    productMaterials += ", bao gồm:<div>";
                }

                foreach (var productFormula in productFormulas)
                {
                    var materialCode = products.FirstOrDefault(m => m.Id == productFormula.MaterialId).Code;
                    var cost = productBranches.FirstOrDefault(m => m.ProductId == productFormula.MaterialId).Cost;
                    totalCost += cost * productFormula.Quantity;
                    productMaterials += $"- [ProductCode]{materialCode}[/ProductCode] : {Normallize(productFormula.Quantity)}*{NormallizeWfp((double)cost)}<br>";
                }
                productMaterials += "</div>";
            }

            return (productMaterials, totalCost);
        }

        private AuditTrailLog GenerateLogPriceBook(ProductDto product, List<PriceBook> priceBooksInfo)
        {
            var contentBuilder = new StringBuilder($"Cập nhật thông tin Bảng giá sản phẩm [ProductCode]{product.Code}[/ProductCode] {product.FullName}<br />");
            contentBuilder.Append("Bảng giá:<br />");
            foreach (var itemPriceBook in product.PriceBookDetails)
            {
                var priceBook = priceBooksInfo.FirstOrDefault(pb => pb.Id == itemPriceBook.PriceBookId);
                if (priceBook != null)
                {
                    contentBuilder.Append($"&nbsp; - {priceBook.Name} : {NormallizeWfp(itemPriceBook.Price)}<br />");
                }
            }

            return new AuditTrailLog
            {
                FunctionId = (int)FunctionType.PriceBook,
                Action = (int)AuditTrailAction.Update,
                BranchId = CurrentBranchId,
                Content = contentBuilder.ToString()
            };
        }

        private AuditTrailLog GenerateLogPriceBook(ProductDto product, ProductCreateDto reqProduct, List<PriceBook> priceBooksInfo)
        {
            var contentBuilder = new StringBuilder($"Cập nhật thông tin Bảng giá sản phẩm [ProductCode]{product.Code}[/ProductCode] {product.FullName}<br />");
            contentBuilder.Append("Bảng giá:<br />");
            foreach (var itemPriceBook in reqProduct.PriceBooks)
            {
                var priceBook = priceBooksInfo.FirstOrDefault(pb => pb.Id == itemPriceBook.PriceBookId);
                if (priceBook != null)
                {
                    contentBuilder.Append($"&nbsp; - {priceBook.Name} : {NormallizeWfp(itemPriceBook.Price)}<br />");
                }
            }

            return new AuditTrailLog
            {
                FunctionId = (int)FunctionType.PriceBook,
                Action = (int)AuditTrailAction.Update,
                BranchId = CurrentBranchId,
                Content = contentBuilder.ToString()
            };
        }

        private async Task<Dictionary<long, string>> GenerateLogProductSaleOnBranch(List<ProductDto> products)
        {
            var result = new Dictionary<long, string>();
            var saleOnBranchs = await ProductSaleBranchRepository.GetProductBranchByProductIds(products.Select(p => p.Id.Value).ToList(), default(CancellationToken));
            var brachInfo = await BranchRepository.GetBranchesAsync();
            foreach (var productId in products.Select(p => p.Id.Value))
            {
                var productSale = saleOnBranchs.Where(p => p.ProductId == productId);
                if (!productSale.Any())
                {
                    result.Add(productId, $"Cho phép kinh doanh: {String.Join(", ", brachInfo.Select(p => p.Name))}");
                    continue;
                }

                var notSale = productSale.Where(p => p.IsActive == false);
                if (notSale.Any())
                {
                    var bracnchIds = notSale.Select(p => p.BranchId);
                    var branchnames = brachInfo.Where(p => bracnchIds.Contains(p.Id)).Select(p => p.Name);

                    if (result.TryGetValue(productId, out string log))
                    {
                        result[productId] = $"{log} <br /> Ngừng kinh doanh: {String.Join(", ", branchnames)}";
                    }
                    else
                    {
                        result.Add(productId, $"<br /> Ngừng kinh doanh: {String.Join(", ", branchnames)}");
                    }
                }

                var allowSaleBranchName = brachInfo.Where(p => !notSale.Select(x => x.BranchId).Contains(p.Id)).Select(p => p.Name).ToList();
                if (allowSaleBranchName.Any())
                {
                    if (result.TryGetValue(productId, out string log))
                    {
                        result[productId] = $"{log} <br /> Cho phép kinh doanh: {String.Join(", ", allowSaleBranchName)}";
                    }
                    else
                    {
                        result.Add(productId, $"<br /> Cho phép kinh doanh: {String.Join(", ", allowSaleBranchName)}");
                    }
                }
            }
            return result;
        }

        private async Task WriteAuditProductUserLog(
            List<ProductUserDto> productUserListToAdd,
            List<ProductUserDto> oldProductUserList,
            List<ProductUserDto> productUserListToDelete,
            List<ProductUserDto> productUsersExisted,
            List<long> productIds)
        {
            try
            {
                var productsResult = await Mediator.Send(new GetProductByIdsQuery(productIds));
                await BuildContentProductUserAuditLogAsync(
                        productUserListToAdd,
                        oldProductUserList,
                        productUserListToDelete,
                        productUsersExisted,
                        productsResult.Data ?? new List<ProductDto>());
            }
            catch (KvException ex)
            {
                Log.Error(ex.Message, ex);
            }
        }

        private async Task BuildContentProductUserAuditLogAsync(
            List<ProductUserDto> productUserListToAdd,
            List<ProductUserDto> oldProductUserList,
            List<ProductUserDto> productUserListToDelete,
            List<ProductUserDto> productUsersUpdated,
            IEnumerable<ProductDto> products)
        {
            var contentSb = new StringBuilder();
            if (productUserListToAdd.Count > 0 || oldProductUserList.Count > 0)
            {
                contentSb.Append("* Danh sách món có sẵn: </br>");
                BuildContentAuditProductUserByAdding(productUserListToAdd, products, contentSb);
                BuildContentAuditProductUserByUpdating(oldProductUserList, productUsersUpdated, products, contentSb);
            }

            if (productUserListToDelete.Count > 0)
            {
                BuildContentAuditByDeleting(productUserListToDelete, products, contentSb);
            }

            var auditLog = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.AvailableProductMenu,
                Action = (int)AuditTrailAction.Update,
                Content = contentSb.ToString()
            };

            if (!string.IsNullOrEmpty(contentSb.ToString()))
                await AuditTrailLogService.AddLog(auditLog);

            contentSb.Clear();
        }

        private void BuildContentAuditProductUserByAdding(
            List<ProductUserDto> productUserListToAdd,
            IEnumerable<ProductDto> products,
            StringBuilder contentSb)
        {
            foreach (var productUser in productUserListToAdd)
            {
                var product = products.FirstOrDefault(p => p.Id == productUser.ProductId);
                if (product == null) continue;
                contentSb.Append($"  - [ProductCode]{product.Code}[/ProductCode] {product.Name}: 0 -> {productUser?.Quantity}</br>");
            }
        }

        private void BuildContentAuditProductUserByUpdating(
            List<ProductUserDto> oldProductUserList,
            List<ProductUserDto> productUsersUpdated,
            IEnumerable<ProductDto> products,
            StringBuilder contentSb)
        {
            foreach (var oldProductUser in oldProductUserList)
            {
                var product = products.FirstOrDefault(p => p.Id == oldProductUser.ProductId);
                if (product == null) continue;
                var productUserUpdated = productUsersUpdated.FirstOrDefault(p => p.ProductId == oldProductUser.ProductId);
                contentSb.Append($"  - [ProductCode]{product.Code}[/ProductCode] {product.Name}: {oldProductUser.Quantity} -> {productUserUpdated?.Quantity}</br>");
            }
        }

        private void BuildContentAuditByDeleting(
            List<ProductUserDto> productUserListToDelete,
            IEnumerable<ProductDto> products,
            StringBuilder contentSb)
        {
            contentSb.Append("* Các món đã xóa khỏi danh sách: </br>");
            foreach (var ProductUser in productUserListToDelete)
            {
                var product = products.FirstOrDefault(p => p.Id == ProductUser.ProductId);
                if (product == null) continue;
                contentSb.Append($"  - [ProductCode]{product.Code}[/ProductCode] {product.Name} </br>");
            }
        }

        private async Task WriteLogProductUser(List<ProductUserModel> productUserModels)
        {
            var contentSb = new StringBuilder();
            if (productUserModels == null || !productUserModels.Any())
            {
                return;
            }
            var productIds = productUserModels.Select(p => p.ProductId).Distinct().ToList();
            var productsResult = await Mediator.Send(new GetProductByIdsQuery(productIds));
            var products = productsResult.Data ?? new List<ProductDto>();

            contentSb.Append("* Danh sách món có sẵn: </br>");
            var productExisted = new List<ProductUserModel>();
            foreach (var productUser in productUserModels)
            {
                var product = products.FirstOrDefault(p => p.Id == productUser.ProductId);
                if (product == null)
                {
                    continue;
                }

                if (productExisted.Any(x => x.ProductId == product.Id && x.BranchId == productUser.BranchId))
                {
                    continue;
                }

                contentSb.Append($"  - [ProductCode]{product.Code}[/ProductCode] {product.Name}: 0 -> {productUser.Quantity}</br>");

                productExisted.Add(productUser);
            }

            AuditTrailLog source = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.AvailableProductMenu,
                Action = (int)AuditTrailAction.Update,
                Content = contentSb.ToString()
            };

            await AuditTrailLogService.AddLog(source);
        }
        #endregion

    }
}