'use strict'

kvApp.controller('NoteTemplateCtrl', ['$scope', 'logger', 'kvMessages', 'kendofilter', '$rootScope', '$filter', 'kvConfirmBox', 'kvSession', 'settings', '$kWindow', 'CommonServices', 'NoteTemplateResource', 'NoteTemplateResourceV2', 'GroupNoteTemplateResource', 'GroupNoteTemplateResourceV2', 'kvLabel', 'LocaleProductGroups', 'FeatureToggleName', 'FeatureToggleService', 'NoteTemplateV3Service', '$translate',
    function ($scope, _logger, _m, _filter, $rootScope, $filter, _msgBox, _session, _settings, $kWindow, CommonServices, noteTemplateResource, noteTemplateResourceV2, GroupNoteTemplateResource, GroupNoteTemplateResourceV2, _l, productGroups, _featureToggleName, _featureToggleService, NoteTemplateV3Service, $translate) {
        $scope.pageSizes = pager.pageSizes;
        $scope.localStoragePagingKey = "noteTemplatePageSettings_" + _session.user.Id;
        $scope.pageSettings = (localStorage[$scope.localStoragePagingKey]) ? angular.fromJson(localStorage[$scope.localStoragePagingKey]) : {};
        if ($scope.pageSettings !== null && $scope.pageSettings.pageSize > 0) {
            $scope.pageSize = $scope.pageSettings.pageSize;
        }
        else {
            $scope.pageSize = pager.defaultList;
        }
        if (($scope.pageSize > pageSizeH.page7 && wHeight < pageSizeH.height) || ($scope.pageSize > pageSizeH.page10 && wHeight > pageSizeH.height)) {
            $(".k-gridNone").addClass("k-scroll");
            $(".k-grid-content").removeClass("k-grid-content-ac");
        } else {
            $(".k-gridNone").removeClass("k-scroll");
        }

        $scope.appSetting = _settings;
        $scope._l = _l;
        $scope.noteTemplateFilter = [];
        $scope.noteTemplateFilter.Name = '';
        $scope.noteTemplateFilter.GroupId = 0;
        $scope.isUsingNoteTemplateApiV3 = _featureToggleService.isEnabled(_featureToggleName.UsingNoteTemplateApiV3);

        $scope.clearSelected = function () {
            if (!$scope.noteTemplateFilter.Name || $scope.noteTemplateFilter.Name.length < 1) {
                _filter.remove($scope.noteTemplate, "Name");
            }
            var filter = [];
            $scope.noteTemplateFilter.GroupId = 0;
            var cond = [];
            cond.push(_filter.newCondition('GroupId', 0, 'ne'));
            filter = _filter.append(filter, cond, "or");
            _filter.addfilter($scope.noteTemplate, filter);
          
            $("#groupNoteTreeView").data("kendoTreeView").select(null);
        };

        $scope.filterByName = function () {
            var filter = [];
            if ($scope.noteTemplateFilter.Name && $scope.noteTemplateFilter.Name.length >= 1) {
                var cond = [];
                cond.push(_filter.newCondition('Name', $scope.noteTemplateFilter.Name, 'contains'));
                filter = _filter.append(filter, cond, "or");
                _filter.addfilter($scope.noteTemplate, filter);
            } else if (!$scope.noteTemplateFilter.Name) {
                filter = $scope.noteTemplate.filter();
                if (!filter) return;
                filter = _filter.detach(filter, 'Name');

                $scope.noteTemplate.filter(filter);
            }
        };

        $scope.filterByGroupNote = function (groupNote) {
            if (!$scope.noteTemplateFilter.Name || $scope.noteTemplateFilter.Name.length < 1) {
                _filter.remove($scope.noteTemplate, "Name");
            }
            var filter = [];
            if (groupNote != null && groupNote.Id > 0) {
                $scope.noteTemplateFilter.GroupId = groupNote.Id;
                var cond = [];
                cond.push(_filter.newCondition('GroupId', $scope.noteTemplateFilter.GroupId, 'eq'));
                filter = _filter.append(filter, cond, "or");
                _filter.addfilter($scope.noteTemplate, filter);
            }
            else if (!$scope.noteTemplateFilter.GroupId) {
                filter = $scope.noteTemplate.filter();
                if (!filter) return;
                filter = _filter.detach(filter, 'GroupId');

                $scope.noteTemplate.filter(filter);
            } else {
                $scope.clearSelected();
            }
        };

        function initFilter() {
            var filter = [];
            return filter;
        };

        var getAdditionalParam = function () {
            var param = {};
            param.FindString = $scope.noteTemplateFilter.Name;
            param.GroupId = $scope.noteTemplateFilter.GroupId;
            return param;
        };

        if ($scope.isUsingNoteTemplateApiV3) {
            $scope.noteTemplate = new kendo.data.DataSource({
                type: "odata",
                transport: {
                    read: function (options) {
                        NoteTemplateV3Service.getNoteTemplatesByGroup(
                            $scope.noteTemplateFilter.GroupId,
                            $scope.noteTemplateFilter.Name,
                            options.data.page,
                            options.data.pageSize,
                            options.data.sort && options.data.sort.length > 0 ? options.data.sort[0].field : "Name",
                            options.data.sort && options.data.sort.length > 0 ? options.data.sort[0].dir : "asc"
                        ).then(function (response) {
                            options.success(response);
                        });
                    }
                },
                pageSize: $scope.pageSize,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
                filter: initFilter(),
                schema: {
                    data: "Data",
                    total: "Total",
                    parse: function (response) {
                        $scope.listNoteTemplate = response.items;
                        return {
                            Data: response.items,
                            Total: response.totalCount
                        };
                    }
                }
            });
        } else {
            $scope.noteTemplate = new kendo.data.DataSource({
                type: "odata",
                transport: {
                    read: {
                        url: "/api/noteTemplates?format=json&Includes=GroupNoteTemplate",
                        dataType: "json",
                        data: getAdditionalParam
                    }
                },
                pageSize: $scope.pageSize,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
                filter: initFilter(),
                schema: {
                    data: "Data",
                    total: "Total",
                    parse: function (response) {
                        if (response) {
                            if (response.Filter) {
                                this.kvFilter = response.Filter;
                            }
                        }
                        $scope.listNoteTemplate = response.Data;
                        return response;
                    }
                }
            });
        }

        $scope.grdColumns = [
            {
                field: "Name",
                title: $translate.instant('man.setting.item_note.table.header.note'),
                headerAttributes: { "class": "cell-name" },
                attributes: { "class": "cell-name" }
            },
            {
                field: "GroupNoteTemplate.Name",
                title: $translate.instant('man.setting.item_note.table.header.group_note'),
                headerAttributes: { "class": "cell-auto" },
                attributes: { "class": "cell-auto" }
            },
            {
                field: "Content",
                title: $translate.instant('man.setting.item_note.table.header.description'),
                headerAttributes: { "class": "cell-description" },
                attributes: { "class": "cell-description" }
            }
        ];

        $scope.refresh = function () {
            $scope.pageSettings.pageSize = parseInt($scope.pageSize);
            localStorage[$scope.localStoragePagingKey] = angular.toJson($scope.pageSettings);
            $scope.noteTemplate.pageSize(parseInt($scope.pageSize));
            if (($scope.pageSize > pageSizeH.page7 && wHeight < pageSizeH.height) || ($scope.pageSize > pageSizeH.page10 && wHeight > pageSizeH.height)) {
                $(".k-gridNone").addClass("k-scroll");
                $(".k-grid-content").removeClass("k-grid-content-ac");
            } else {
                $(".k-gridNone").removeClass("k-scroll");
            }
        };

        $scope.deleteNoteTemplate = function (noteTemplate) {
            _msgBox.confirm($translate.instant('man.setting.item_note.dialog.delete.content'), $translate.instant('man.setting.item_note.dialog.delete.title'), false, $translate.instant('common.button.confirm'), $translate.instant('common.button.cancel'), false, true);
            _msgBox.onConfirm = function () {
                if ($scope.isUsingNoteTemplateApiV3) {
                    NoteTemplateV3Service.deleteNoteTemplate(noteTemplate.Id).then(function () {
                        _logger.success($translate.instant('man.setting.item_note.toast_message.delete_success'));
                        $scope.noteTemplate.read();
                    }, function (err) {
                        _logger.error(getMessage(err));
                    });
                }
                else {
                    noteTemplateResourceV2.remove({ Id: noteTemplate.Id, GroupId: noteTemplate.GroupId }, function (resp) {
                        _logger.success($translate.instant('man.setting.item_note.toast_message.delete_success'));
                        $scope.noteTemplate.read();
                }, function (err) {
                        _logger.error(getMessage(err));
                    });
                }
            };

        };

        $scope.createOrUpdate = function (noteTemplate) {
            var title = '';
            if (!noteTemplate) {
                title = $translate.instant('man.setting.item_note.create.title');
            }
            else {
                title = $translate.instant('man.setting.item_note.update.title');
            }

            var wdInstance = $kWindow.open({
                options: {
                    modal: true,
                    title: title,
                    resizable: false,
                    draggable: true,
                    pinned: true,
                    width: 650,
                    visible: false,
                    open: function () {
                        this.wrapper.addClass("k-window-poup k-window-fix k-window-noteTemplate kv-window");
                    },
                    close: function () {

                    },
                    activate: function () {
                        $(".iptFocus").focus();
                        $('.k-pager-wrap').each(function () {
                            if (!$(this).parent().hasClass('paging-box')) {
                                $(this).wrap('<div class="paging-box"></div>');
                            }
                        })
                    }
                },
                template: '<kv-create-or-update-note-template form-name="noteTemplateForm" note-template-listeners="listeners" note-template="noteTemplate"></kv-create-or-update-note-template>',
                controller: ["$scope", "$windowInstance", function ($scopeWindow, $windowInstance) {
                    $scopeWindow.listeners = {
                        onSave: function (noteTemplate) {
                            $windowInstance.close();
                            $scope.noteTemplate.read().then(function () {
                                $("tr.k-master-row").each(function () {
                                    if (noteTemplate.Data.Id == $(this).find("td.tdCode span").attr("data-code")) {
                                        $('#noteTemplate').data().kendoGrid.expandRow($(this));
                                        return;
                                    }
                                });
                            });
                        },
                        onCancel: function () {
                            $windowInstance.close();
                        }
                    };

                    $scopeWindow.noteTemplate = angular.copy(noteTemplate);
                    refresh($scope);
                }],
                resolve: {
                }
            });

            wdInstance.result.then(function (resp) {
                if (angular.isObject(resp) && resp) {
                }
            });
        }

        $scope.bindedGrid;
        $scope.grvdataBinding = function (arg) {
            $("span.line").remove();
            $scope.bindedGrid = arg.sender;
            if ($scope.isUsingNoteTemplateApiV3) {
                if (!$scope.$$phase) {
                    $scope.$apply();
                }
            }
            else {
                $scope.$apply();
            }
        };

        $scope.grvNoteTemplateDataBound = function (arg) {
            $scope.emptyGridFix(arg);
        }

        $scope.detailTemplate = kendo.template($('#noteTemplateDetailTmpl').html());

        var tabH = [];
        $scope.grvDetailInit = function (e) {
            var detailRow = e.detailRow;
            tabH[e.data.uid] = [];
            var tabship = detailRow.find(".tabstrip").kendoTabStrip({
                animation: false,
                activate: function (tabevent) {
                    var calcH = function () {
                        setTimeout(function () {
                            tabH[e.data.uid][tabevent.item.id] = $(".k-master-row.k-master-state").next().find(".k-detail-cell").height() + $('.k-master-row.k-master-state').height();
                            $('span.line').height(tabH[e.data.uid][tabevent.item.id]);
                        }, 1);
                    }
                    calcH();
                }
            });
            var hideTab = function (index) {
                $(tabship.data("kendoTabStrip").items()[index]).attr("style", "display:none");
            };
            var pageableObj = { pageSize: 10, refresh: false, "pageSizes": false, buttonCount: 5, messages: { display: $translate.instant('man.table_pagination.label.display_number') + $translate.instant('man.list.table_pagination.label.product') } };
            function detailFilter(name, typeFilter) {
                var filter = [];

                filter = _filter.append(filter, [_filter.newCondition('Name', name, "eq")], "and");
                return filter;
            }

            if ($scope.isUsingNoteTemplateApiV3) {
                var noteTemplateDetails = new kendo.data.DataSource({
                    transport: {
                        read: function (options) {
                            NoteTemplateV3Service.getProductsForNoteTemplate(
                                e.data.Id,
                                options.data.page,
                                options.data.pageSize
                            ).then(function (response) {
                                options.success(response);
                            });
                        }
                    },
                    change: function () {
                        if (this._total === 0) {
                            hideTab(1);
                        }
                    },
                    pageSize: 10,
                    serverPaging: true,
                    serverFiltering: true,
                    serverSorting: true,
                    schema: {
                        data: 'Data',
                        total: 'Total',
                        parse: function (response) {
                            angular.forEach(response.items, function (r) {
                                r.ProductGroupName = productGroups[r.ProductGroup || 3];
                            });

                            return {
                                Data: response.items,
                                Total: response.totalCount
                            }
                        },
                        model: {
                            fields: {
                                Code: { type: "string" },
                                Name: { type: "string" }
                            }
                        }
                    }
                });
            }
            else {
                var url = '/api/v2/note-templates/' + e.data.Id + '/get-products';

            var noteTemplateDetails = new kendo.data.DataSource({
                transport: {
                    read: {
                        url: url,
                        dataType: 'json'
                    }
                },
                change: function () {
                    if (this._total === 0) {
                        hideTab(1);
                    }
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
                schema: {
                    data: 'Data',
                    total: 'Total',
                    parse: function (response) {
                        if (response) {
                            if (response.Filter) {
                                this.kvFilter = response.Filter;
                            }
                        }

                        angular.forEach(response.Data, function (r) {
                            r.ProductGroupName = productGroups[r.ProductGroup || 3];
                        });

                        return response;
                    },
                    model: {
                        fields: {
                            Code: { type: "string" },
                            Name: { type: "string" }
                            }
                        }
                    }
                });
            }

            detailRow.find('.selectedProductList').kendoGrid({
                dataSource: noteTemplateDetails,
                pageable: pageableObj,
                dataBinding: function () {
                    $scope.emptyGridFix.apply(this, arguments);
                },
                dataBound: function () {
                },
                columns: [
                    { field: "Code", title: $translate.instant('man.setting.item_note.item_list.table.header.item_code'), template: '<a href="/\\\\#/#=viewUrl.productList#?Code=#=encodeURIComponent(Code)#" target="_blank" class="orderCode" data-id="#=Id#">#:Code#</a>', headerAttributes: { "class": "tdCode" }, attributes: { "class": "tdCode" } },
                    { field: "FullName", title: $translate.instant('man.setting.item_note.item_list.table.header.item_name'), headerAttributes: { "class": "tdProductName" }, attributes: { "class": "tdProductName" } },
                    { field: "ProductGroupName", title: $translate.instant('man.setting.item_note.item_list.table.header.menu_type'), headerAttributes: { "class": "tdProductGroupName" }, attributes: { "class": "tdProductGroupName" } },
                    { field: "CategoryName", title: $translate.instant('man.setting.item_note.item_list.table.header.category'), headerAttributes: { "class": "tdCategoryName" }, attributes: { "class": "tdCategoryName" } }
                ]
            });
        };

        $scope.groupNoteTreeData = new kendo.data.HierarchicalDataSource({
            schema: {
                model: {
                    children: "items",
                    id: "Id",
                    hidden: "hidden"
                }
            },
            transport: {
                read: function (options) {
                    if ($scope.groupNoteHierarchicalData) {
                        options.success($scope.groupNoteHierarchicalData.filter(function (itm) { return !itm.hidden }));
                        return;
                    }
                    options.success([]);
                }
            }

        });

        function onSelectGroupNoteTree (e) {
            $(".groupNoteTree").find(".kv-state-selected").removeClass('kv-state-selected');
            $(".groupNoteTree").find(".kv-root-state-selected").removeClass('kv-root-state-selected');
            var node = e && e.node ? e.node : e;
            $(node).addClass('kv-state-selected');

            var treeView = $("#groupNoteTreeView").data("kendoTreeView");

            var dataItem = treeView.dataItem(node);
            $scope.onSelected && $scope.onSelected(dataItem);
        }

        $scope.groupNoteTreeOptions = {
            loadOnDemand: true,
            select: onSelectGroupNoteTree,
            dragAndDrop: false,
            dataBound: function (e) {
                var allSelected = this.dataSource.get(-1);
                if (allSelected) {
                    var element = this.findByUid(allSelected.uid)
                    this.select(element);
                    onSelectGroupNoteTree(element);
                }
            }
        }

        $scope.loadData = function (onReady, expandPoint) {
            if ($scope.isUsingNoteTemplateApiV3) {
                NoteTemplateV3Service.getAllGroupNoteTemplate().then(function (response) {
                    $scope.groupNoteHierarchicalData = [];
                    if (response) 
                        $scope.groupNoteHierarchicalData = response.data;
                    $scope.groupNoteHierarchicalData.unshift({ Id: -1, Name: $translate.instant('man.setting.item_note.filer.group.value.all') });

                    if ($scope.groupNoteTreeData != null) {
                        $scope.groupNoteTreeData.data($scope.groupNoteHierarchicalData);
                    }

                    onReady && onReady();
                    
                    if (expandPoint)
                        expandPoint.forEach(function (obj) {
                            $('#groupNoteTreeView').data('kendoTreeView').expandPath([obj]);
                        });
                });
            }
            else {
                GroupNoteTemplateResourceV2.get({}, function (response) {
                    $scope.groupNoteHierarchicalData = [];
                    if (response.Data) 
                        $scope.groupNoteHierarchicalData = response.Data;
                    $scope.groupNoteHierarchicalData.unshift({ Id: -1, Name: $translate.instant('man.setting.item_note.filer.group.value.all') });

                    if ($scope.groupNoteTreeData != null) {
                        $scope.groupNoteTreeData.data($scope.groupNoteHierarchicalData);
                    }
        
                    onReady && onReady();
                    
                    if (expandPoint)
                        expandPoint.forEach(function (obj) {
                            $('#groupNoteTreeView').data('kendoTreeView').expandPath([obj]);
                        });
                });
            }
        }

        $scope.loadData();

        $rootScope.$on('reloadGroupNote', function () {
            $scope.loadData();
        });

        $rootScope.$on('reloadNoteTemplate', function () {
            $scope.noteTemplate.read();
        });

        if ($scope.isUsingNoteTemplateApiV3) {
            $scope.groupNoteTemplate = new kendo.data.DataSource({
                type: "odata",
                transport: {
                    read: function (options) {
                        NoteTemplateV3Service.getAllGroupNoteTemplate().then(function (response) {
                            options.success(response);
                        });
                    }
                },
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
                schema: {
                    data: "Data",
                    total: "Total",
                    parse: function (response) {
                        return {
                            Data: response.data,
                            Total: response.total
                        };
                    }
                }
            });
        }
        else {
            $scope.groupNoteTemplate = new kendo.data.DataSource({
                type:  null,
                transport: {
                    read: {
                        url: "/api/v2/group-note-templates?format=json",
                        dataType: "json" // "jsonp" is required for cross-domain requests; use "json" for same-domain requests
                    }
                },
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
                schema: {
                    data: "Data",
                    total: "Total",
                    parse: function (response) {
                        if (response) {
                            if (response.Filter) {
                                this.kvFilter = response.Filter;
                            }
                        }
                        return response;
                    }
                }
            });
        }


        $scope.createOrUpdateGroupNote = function (groupNoteTemplate) {
            var title = '';
            if (!groupNoteTemplate) {
                title = $translate.instant('man.setting.item_note.create_group.title');
            }
            else {
                title = $translate.instant('man.setting.item_note.update_group.title');
            }

            var wdInstance = $kWindow.open({
                options: {
                    modal: true,
                    title: title,
                    resizable: false,
                    draggable: true,
                    pinned: true,
                    width: 438,
                    visible: false,
                    open: function () {
                        this.wrapper.addClass("k-window-poup k-window-groupNoteTemplate kv-window");
                    },
                    close: function () {

                    },
                    activate: function () {
                        $(".iptFocus").focus();
                        $('.k-pager-wrap').each(function () {
                            if (!$(this).parent().hasClass('paging-box')) {
                                $(this).wrap('<div class="paging-box"></div>');
                            }
                        })
                    }
                },
                template: '<kv-create-or-update-group-note-template form-name="groupNoteTemplateForm" group-note-template-listeners="listeners" group-note-template="groupNoteTemplate" list-note-template="listNoteTemplate"></kv-create-or-update-group-note-template>',
                controller: ["$scope", "$windowInstance", function ($scopeWindow, $windowInstance) {
                    $scopeWindow.listeners = {
                        onSave: function (groupNoteTemplate) {
                            $windowInstance.close();
                            $scope.groupNoteTemplate.read().then(function () {
                              
                            });
                            $scope.noteTemplate.read();
                        },
                        onCancel: function () {
                            $windowInstance.close();
                        }
                    };
                    $scopeWindow.listNoteTemplate = angular.copy($scope.listNoteTemplate);
                    $scopeWindow.groupNoteTemplate = angular.copy(groupNoteTemplate);
                    refresh($scope);
                }],
                resolve: {
                }
            });

            wdInstance.result.then(function (resp) {
                if (angular.isObject(resp) && resp) {
                }
            });
        }
    }])
